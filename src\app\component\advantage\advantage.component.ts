import { Component, Inject, PLATFORM_ID, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ScreenService } from '../../service/screen/screen.service';
import { CommonModule, isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-advantage',
  imports: [
    CommonModule
  ],
  templateUrl: './advantage.component.html',
  styleUrl: './advantage.component.css'
})
export class AdvantageComponent implements AfterViewInit {
  translatePage: any;
  isVisible = false;
  isLoading = true;

  @ViewChild('advantageSection') advantageSection!: ElementRef;

  constructor(
    private translate: TranslateService,
    private readonly screenService: ScreenService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { }

  async ngOnInit() {
    var lang = 'vi';
    if (isPlatformBrowser(this.platformId)) {
      lang = localStorage.getItem('lang') || 'vi';
    }
    this.translate.use(lang);

    this.translate.onLangChange.subscribe(() => {
      this.loadTransLate(this.translate.currentLang);
    });
  }

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.platformId)) {
    this.setupIntersectionObserver();
    }
  }

  private setupIntersectionObserver(): void {
    const options = {
      root: null,
      rootMargin: '100px',
      threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.isVisible) {
          this.isVisible = true;
          this.onVisible();
          observer.unobserve(entry.target);
        }
      });
    }, options);

    if (this.advantageSection) {
      observer.observe(this.advantageSection.nativeElement);
    }
  }

  async onVisible() {
    this.isLoading = true;
    try {
      await this.loadTransLate(this.translate.currentLang);
    } finally {
      this.isLoading = false;
    }
  }

  async loadTransLate(lang: string = 'vi') {
    var request = {
      Language: lang,
      ScreenCode: 'S0004-0006'
    }
    const observable = await this.screenService.getPageTranslate(request);
    const res = await observable.toPromise();
    try {
      if (res.isSuccessed) {
        this.translatePage = res.resultObj;
      }
    } catch (error) {
      console.log(error);
    }
  }
}
