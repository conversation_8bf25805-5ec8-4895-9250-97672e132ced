import { CommonModule } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { EmailContactComponent } from '../common/email-contact/email-contact.component';
import { RouterLink } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ScreenService } from '../../service/screen/screen.service';
import { Title } from '@angular/platform-browser';
import { SlickCarouselComponent, SlickCarouselModule } from 'ngx-slick-carousel';
import { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
interface TimelineItem {
  id: number
  year: string
  content: string
}
@Component({
  selector: 'app-about-us',
  imports: [
    CommonModule,
    EmailContactComponent,
    RouterLink,
    SlickCarouselModule,
    NgbNavModule
  ],
  templateUrl: './about-us.component.html',
  styleUrl: './about-us.component.css'
})
export class AboutUsComponent {
  @ViewChild('slickModal') slickModal: SlickCarouselComponent | undefined;
  @ViewChild('slickModal2') slickModal2: SlickCarouselComponent | undefined;
  @ViewChild('slickModal3') slickModal3: SlickCarouselComponent | undefined;
  timelineData: TimelineItem[] = [];

  selectedItem: TimelineItem | null = null;
  hoveredItem: number | null = null;

  selectItem(item: any) {
    this.selectedItem = null; // Temporarily set to null to trigger ngIf
    setTimeout(() => {
      this.selectedItem = item;
    }, 0); // Reassign the selected item after a short delay
  }
  hoverItem(itemId: number | null): void {
    this.hoveredItem = itemId;
  }
  active = 1;
  activeHotel = 1;
  translatePage: any;

  boxes = [
    {
      emoji: '🏢',
      title: 'S0004-0002_0018',
      description: 'S0004-0002_0019',
      link: '/corporate-business',
    },
    {
      emoji: '🎫',
      title: 'S0004-0002_0020',
      description: 'S0004-0002_0021',
      link: '/f2',
    },
    {
      emoji: '🏝️',
      title: 'S0004-0002_0022',
      description: 'S0004-0002_0023',
      link: '/tours',
    },
    {
      emoji: '✈️',
      title: 'S0004-0002_0024',
      description: 'S0004-0002_0025',
      link: '/',
    },
    {
      emoji: '🏨',
      title: 'S0004-0002_0026',
      description: 'S0004-0002_0063',
      link: '/hotel',
    },
  ];



  constructor(
    private readonly screenService: ScreenService,
    private translate: TranslateService,
    private titleService: Title
  ) { }

  async ngOnInit() {
    await this.loadTransLate(this.translate.currentLang);

    this.translate.onLangChange.subscribe(() => {
      this.loadTransLate(this.translate.currentLang);
    });
  }

  get visibleItems() {
    return this.boxes;
  }

  getTimelineFillWidth(): number {
    if (!this.selectedItem) {
      return 0;
    }
    const selectedIndex = this.timelineData.findIndex(item => item.id === this.selectedItem?.id);
    return ((selectedIndex) / (this.timelineData.length - 1)) * 100;
  }


  async loadTransLate(lang: string = 'vi') {
    var request = {
      Language: lang,
      ScreenCode: 'S0004-0002'
    }
    const observable = await this.screenService.getPageTranslate(request);
    const res = await observable.toPromise();
    try {
      if (res.isSuccessed) {
        this.translatePage = res.resultObj;
        this.titleService.setTitle(this.translatePage?.['S0004-0002_0001'] + ' - ' + "Ngọc Mai Travel");
        this.timelineData = [
          {
            id: 1, year: '2016', content: `<ul class="list-disc pl-5">
                                                          <li>${this.translatePage?.['S0004-0002_0011']} </li>
                                                          <li>${this.translatePage?.['S0004-0002_0057']} </li>
                                                      </ul>` },
          { id: 2, year: '2017', content: `${this.translatePage?.['S0004-0002_0012']}` },
          { id: 3, year: '2018', content: `${this.translatePage?.['S0004-0002_0013']}` },
          {
            id: 4, year: '2019', content: `<ul class="list-disc pl-5">
                                                          <li>${this.translatePage?.['S0004-0002_0014']} </li>
                                                          <li>${this.translatePage?.['S0004-0002_0085']} </li>
                                                      </ul>` },
          {
            id: 5, year: '2020 - 2022', content: `<div class="text-gray-700 text-sm md:text-base leading-relaxed">
                                                <p class="mb-2">${this.translatePage?.['S0004-0002_0015']}</p>
                                                <ul class="list-disc pl-5">
                                                    <li>${this.translatePage?.['S0004-0002_0058']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0059']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0060']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0061']}</li>
                                                </ul>
                                            </div>`
          },
          {
            id: 6, year: '2022 – Nay', content: `<div class="text-gray-700 text-sm md:text-base leading-relaxed">
                                                <p class="mb-2">${this.translatePage?.['S0004-0002_0086']}</p>
                                                <ul class="list-disc pl-5">
                                                    <li><strong>${this.translatePage?.['S0004-0002_0096']}</strong></li>
                                                    <li>${this.translatePage?.['S0004-0002_0087']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0088']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0089']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0090']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0091']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0092']}</li>
                                                    <li>${this.translatePage?.['S0004-0002_0093']}</li>
                                                </ul>
                                            </div>`
          }
        ];
        if (lang !== 'vi') {
          this.timelineData[5].year = '2022 – Now';
        }
        this.selectedItem = this.timelineData[0];
      }
    } catch (error) {
      console.log(error);
    }

  }

  next() {
    this.slickModal?.slickNext();
  }

  prev() {
    this.slickModal?.slickPrev();
  }

  next2() {
    this.slickModal2?.slickNext();
  }

  prev2() {
    this.slickModal2?.slickPrev();
  }

  next3() {
    this.slickModal3?.slickNext();
  }

  prev3() {
    this.slickModal3?.slickPrev();
  }

  slideConfig = {
    slidesToShow: 4,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    infinite: false,
    autoplay: false,
    autoplaySpeed: 5000,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
        },
      }

    ],
  };

  slideConfig2 = {
    slidesToShow: 4,
    slidesToScroll: 1,
    arrows: false,
    dots: false,
    infinite: false,
    autoplay: false,
    autoplaySpeed: 5000,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
        }
      }

    ],
  };

}
