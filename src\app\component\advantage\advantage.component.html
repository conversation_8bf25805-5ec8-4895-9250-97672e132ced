<div #advantageSection class="dark:bg-gray-700 bg-center">
    <div class="w-full  max-md:px-2 bg-gray-50 dark:bg-gray-700 bg-center gap-2">
        <div class="w-full overflow-hidden whitespace-nowrap">
            <div class="marquee-content flex gap-4 py-4 space-x-2">
                <!-- Skeleton Loading -->
                <ng-container *ngIf="isLoading">
                    <div class="h-full" *ngFor="let i of [1,2,3,4,5,6,7]">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
                            </span>
                            <div>
                                <div class="w-32 h-4 bg-gray-200 dark:bg-gray-600 rounded animate-pulse mb-2"></div>
                                <div class="w-48 h-3 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                </ng-container>

                <!-- Actual Content -->
                <ng-container *ngIf="!isLoading">
                    <div class="h-full">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 448 512">
                                    <path
                                        d="M0 80L0 229.5c0 17 6.7 33.3 18.7 45.3l176 176c25 25 65.5 25 90.5 0L418.7 317.3c25-25 25-65.5 0-90.5l-176-176c-12-12-28.3-18.7-45.3-18.7L48 32C21.5 32 0 53.5 0 80zm112 32a32 32 0 1 1 0 64 32 32 0 1 1 0-64z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0002'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0003'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-full">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 512 512">
                                    <path
                                        d="M256 48C141.1 48 48 141.1 48 256l0 40c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-40C0 114.6 114.6 0 256 0S512 114.6 512 256l0 144.1c0 48.6-39.4 88-88.1 88L313.6 488c-8.3 14.3-23.8 24-41.6 24l-32 0c-26.5 0-48-21.5-48-48s21.5-48 48-48l32 0c17.8 0 33.3 9.7 41.6 24l110.4 .1c22.1 0 40-17.9 40-40L464 256c0-114.9-93.1-208-208-208zM144 208l16 0c17.7 0 32 14.3 32 32l0 112c0 17.7-14.3 32-32 32l-16 0c-35.3 0-64-28.7-64-64l0-48c0-35.3 28.7-64 64-64zm224 0c35.3 0 64 28.7 64 64l0 48c0 35.3-28.7 64-64 64l-16 0c-17.7 0-32-14.3-32-32l0-112c0-17.7 14.3-32 32-32l16 0z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0004'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0005'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-full">
                        <div class="flex gap-2 items-center  py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 512 512">
                                    <path
                                        d="M184 48l144 0c4.4 0 8 3.6 8 8l0 40L176 96l0-40c0-4.4 3.6-8 8-8zm-56 8l0 40L64 96C28.7 96 0 124.7 0 160l0 96 192 0 128 0 192 0 0-96c0-35.3-28.7-64-64-64l-64 0 0-40c0-30.9-25.1-56-56-56L184 0c-30.9 0-56 25.1-56 56zM512 288l-192 0 0 32c0 17.7-14.3 32-32 32l-64 0c-17.7 0-32-14.3-32-32l0-32L0 288 0 416c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-128z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0006'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0007'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-full">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 576 512">
                                    <path
                                        d="M316.9 18C311.6 7 300.4 0 288.1 0s-23.4 7-28.8 18L195 150.3 51.4 171.5c-12 1.8-22 10.2-25.7 21.7s-.7 24.2 7.9 32.7L137.8 329 113.2 474.7c-2 12 3 24.2 12.9 31.3s23 8 33.8 2.3l128.3-68.5 128.3 68.5c10.8 5.7 23.9 4.9 33.8-2.3s14.9-19.3 12.9-31.3L438.5 329 542.7 225.9c8.6-8.5 11.7-21.2 7.9-32.7s-13.7-19.9-25.7-21.7L381.2 150.3 316.9 18z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0008'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0009'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-full">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 640 512">
                                    <path
                                        d="M256 64l128 0 0 64-128 0 0-64zM240 0c-26.5 0-48 21.5-48 48l0 96c0 26.5 21.5 48 48 48l48 0 0 32L32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l96 0 0 32-48 0c-26.5 0-48 21.5-48 48l0 96c0 26.5 21.5 48 48 48l160 0c26.5 0 48-21.5 48-48l0-96c0-26.5-21.5-48-48-48l-48 0 0-32 256 0 0 32-48 0c-26.5 0-48 21.5-48 48l0 96c0 26.5 21.5 48 48 48l160 0c26.5 0 48-21.5 48-48l0-96c0-26.5-21.5-48-48-48l-48 0 0-32 96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-256 0 0-32 48 0c26.5 0 48-21.5 48-48l0-96c0-26.5-21.5-48-48-48L240 0zM96 448l0-64 128 0 0 64L96 448zm320-64l128 0 0 64-128 0 0-64z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0010'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0011'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-full">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 512 512">
                                    <path
                                        d="M78.6 5C69.1-2.4 55.6-1.5 47 7L7 47c-8.5 8.5-9.4 22-2.1 31.6l80 104c4.5 5.9 11.6 9.4 19 9.4l54.1 0 109 109c-14.7 29-10 65.4 14.3 89.6l112 112c12.5 12.5 32.8 12.5 45.3 0l64-64c12.5-12.5 12.5-32.8 0-45.3l-112-112c-24.2-24.2-60.6-29-89.6-14.3l-109-109 0-54.1c0-7.5-3.5-14.5-9.4-19L78.6 5zM19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L233.7 374.3c-7.8-20.9-9-43.6-3.6-65.1l-61.7-61.7L19.9 396.1zM512 144c0-10.5-1.1-20.7-3.2-30.5c-2.4-11.2-16.1-14.1-24.2-6l-63.9 63.9c-3 3-7.1 4.7-11.3 4.7L352 176c-8.8 0-16-7.2-16-16l0-57.4c0-4.2 1.7-8.3 4.7-11.3l63.9-63.9c8.1-8.1 5.2-21.8-6-24.2C388.7 1.1 378.5 0 368 0C288.5 0 224 64.5 224 144l0 .8 85.3 85.3c36-9.1 75.8 .5 104 28.7L429 274.5c49-23 83-72.8 83-130.5zM56 432a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0012'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0013'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="h-full">
                        <div class="flex gap-2 items-center py-2">
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-8 fill-primary-600"
                                    viewBox="0 0 640 512">
                                    <path
                                        d="M144 0a80 80 0 1 1 0 160A80 80 0 1 1 144 0zM512 0a80 80 0 1 1 0 160A80 80 0 1 1 512 0zM0 298.7C0 239.8 47.8 192 106.7 192l42.7 0c15.9 0 31 3.5 44.6 9.7c-1.3 7.2-1.9 14.7-1.9 22.3c0 38.2 16.8 72.5 43.3 96c-.2 0-.4 0-.7 0L21.3 320C9.6 320 0 310.4 0 298.7zM405.3 320c-.2 0-.4 0-.7 0c26.6-23.5 43.3-57.8 43.3-96c0-7.6-.7-15-1.9-22.3c13.6-6.3 28.7-9.7 44.6-9.7l42.7 0C592.2 192 640 239.8 640 298.7c0 11.8-9.6 21.3-21.3 21.3l-213.3 0zM224 224a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zM128 485.3C128 411.7 187.7 352 261.3 352l117.3 0C452.3 352 512 411.7 512 485.3c0 14.7-11.9 26.7-26.7 26.7l-330.7 0c-14.7 0-26.7-11.9-26.7-26.7z" />
                                </svg>
                            </span>
                            <div>
                                <strong class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0014'] }}
                                </strong>
                                <div class="dark:text-white">
                                    {{translatePage?.['S0004-0006_0015'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
</div>