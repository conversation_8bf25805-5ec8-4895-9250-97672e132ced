@keyframes float {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-25px) translateX(15px) rotate(3deg);
  }

  66% {
    transform: translateY(-12px) translateX(-8px) rotate(-2deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(40px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 9s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

.perspective-1000 {
  perspective: 1000px;
}

.rotateY-12 {
  transform: rotateY(12deg);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-20px) rotate(2deg);
  }

  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-20px) translateX(10px) rotate(1deg);
  }

  66% {
    transform: translateY(-10px) translateX(-5px) rotate(-1deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-15px) translateX(8px) rotate(2deg);
  }

  66% {
    transform: translateY(-8px) translateX(-4px) rotate(-1deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 7s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}
@keyframes star-burst-multi {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) rotate(0deg);
  }

  10% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2) rotate(20deg) translateY(0)
      translateX(0);
  }

  40% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1) rotate(0deg)
      translateY(calc(-1 * var(--distance, 40px))) translateX(0px);
  }

  60% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(0.8) rotate(-10deg)
      translateY(calc(-1 * var(--distance, 40px) * 1.5)) translateX(0px);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) rotate(0deg)
      translateY(calc(-1 * var(--distance, 40px) * 2)) translateX(0px);
  }
}

.star-burst-multi {
  opacity: 0;
  transform: translate(-50%, -50%) rotate(var(--angle, 0deg));
  pointer-events: none;
  animation: star-burst-multi 1.6s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  animation-delay: var(--delay, 0s);
}

/* Enhanced Button Animations for Book ngay */
@keyframes starPulse {
  0%, 100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.2);
  }
}

@keyframes starBurst {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  30% {
    opacity: 1;
    transform: scale(1.3) rotate(180deg);
  }
  70% {
    opacity: 0.8;
    transform: scale(1) rotate(360deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(540deg);
  }
}

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: scale(1.2) rotate(90deg);
  }
  50% {
    opacity: 0.6;
    transform: scale(1) rotate(180deg);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.1) rotate(270deg);
  }
}

@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(249, 115, 22, 0.2),
      0 0 40px rgba(239, 68, 68, 0.15), 
      0 0 60px rgba(236, 72, 153, 0.1),
      inset 0 0 20px rgba(249, 115, 22, 0.1);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(249, 115, 22, 0.4),
      0 0 60px rgba(239, 68, 68, 0.3), 
      0 0 90px rgba(236, 72, 153, 0.2),
      inset 0 0 30px rgba(249, 115, 22, 0.2);
  }
}

@keyframes buttonPulse {
  0%, 100% {
    transform: scale(1);
    border-color: rgba(249, 115, 22, 0.8);
  }
  50% {
    transform: scale(1.02);
    border-color: rgba(239, 68, 68, 0.9);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(300%) skewX(-12deg);
  }
}

/* ===== EXPLOSIVE BUTTON ANIMATIONS ===== */

/* Hiệu ứng nền gradient động với sóng */
@keyframes backgroundWaves {
  0% {
    background-position: 0% 50%;
    transform: scale(1);
  }
  25% {
    background-position: 50% 100%;
    transform: scale(1.02);
  }
  50% {
    background-position: 100% 50%;
    transform: scale(1.05);
  }
  75% {
    background-position: 50% 0%;
    transform: scale(1.02);
  }
  100% {
    background-position: 0% 50%;
    transform: scale(1);
  }
}

/* Hiệu ứng shimmer bùng nổ */
@keyframes shimmerExplosive {
  0% {
    transform: translateX(-100%) skewX(-15deg) scale(0.8);
    opacity: 0;
  }
  30% {
    opacity: 1;
    transform: translateX(-50%) skewX(-10deg) scale(1.2);
  }
  70% {
    opacity: 0.8;
    transform: translateX(50%) skewX(-5deg) scale(1.1);
  }
  100% {
    transform: translateX(300%) skewX(-15deg) scale(0.8);
    opacity: 0;
  }
}

/* Hiệu ứng tia sáng bùng nổ */
@keyframes lightRay {
  0% {
    opacity: 0;
    transform: scale(0) rotate(var(--rotation));
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(calc(var(--rotation) + 180deg));
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(calc(var(--rotation) + 360deg));
  }
}

/* Hiệu ứng ngôi sao bùng nổ trung tâm */
@keyframes starExplosion {
  0% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(0.5) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.8) rotate(90deg);
  }
  50% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1.5) rotate(180deg);
  }
  75% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(2) rotate(270deg);
  }
  100% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(0.5) rotate(360deg);
  }
}

/* Hiệu ứng ngôi sao bùng nổ xung quanh */
@keyframes starBurstExplosive {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  20% {
    opacity: 1;
    transform: scale(2) rotate(120deg);
  }
  60% {
    opacity: 0.8;
    transform: scale(1.5) rotate(240deg);
  }
  80% {
    opacity: 1;
    transform: scale(2.2) rotate(300deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

/* Hiệu ứng ngôi sao lấp lánh */
@keyframes starSparkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: scale(1.5) rotate(90deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2) rotate(180deg);
  }
  75% {
    opacity: 1;
    transform: scale(1.8) rotate(270deg);
  }
}

/* Hiệu ứng particles bùng nổ */
@keyframes particleExplosion {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0);
  }
  30% {
    opacity: 1;
    transform: scale(1) translate(var(--dx), var(--dy));
  }
  70% {
    opacity: 0.8;
    transform: scale(0.8) translate(calc(var(--dx) * 2), calc(var(--dy) * 2));
  }
  100% {
    opacity: 0;
    transform: scale(0) translate(calc(var(--dx) * 3), calc(var(--dy) * 3));
  }
}

/* Hiệu ứng button glow bùng nổ */
@keyframes buttonGlowExplosive {
  0%, 100% {
    box-shadow: 
      0 0 30px rgba(249, 115, 22, 0.5),
      0 0 60px rgba(239, 68, 68, 0.4), 
      0 0 90px rgba(236, 72, 153, 0.3),
      0 0 120px rgba(168, 85, 247, 0.2),
      inset 0 0 30px rgba(249, 115, 22, 0.2);
  }
  50% {
    box-shadow: 
      0 0 50px rgba(249, 115, 22, 0.8),
      0 0 100px rgba(239, 68, 68, 0.7), 
      0 0 150px rgba(236, 72, 153, 0.6),
      0 0 200px rgba(168, 85, 247, 0.4),
      inset 0 0 50px rgba(249, 115, 22, 0.4);
  }
}

/* Hiệu ứng text bùng nổ */
@keyframes textExplosive {
  0%, 100% {
    text-shadow: 
      0 0 10px rgba(249, 115, 22, 0.8),
      0 0 20px rgba(239, 68, 68, 0.6),
      0 0 30px rgba(236, 72, 153, 0.4),
      2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow: 
      0 0 20px rgba(249, 115, 22, 1),
      0 0 40px rgba(239, 68, 68, 0.9),
      0 0 60px rgba(236, 72, 153, 0.7),
      0 0 80px rgba(168, 85, 247, 0.5),
      3px 3px 6px rgba(0, 0, 0, 0.4);
  }
}

/* Hiệu ứng icon bùng nổ */
@keyframes iconExplosive {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 10px rgba(249, 115, 22, 0.6));
  }
  25% {
    transform: scale(1.3) rotate(10deg);
    filter: drop-shadow(0 0 20px rgba(239, 68, 68, 0.8));
  }
  50% {
    transform: scale(1.2) rotate(-5deg);
    filter: drop-shadow(0 0 25px rgba(236, 72, 153, 0.9));
  }
  75% {
    transform: scale(1.4) rotate(15deg);
    filter: drop-shadow(0 0 30px rgba(168, 85, 247, 0.7));
  }
}

/* ===== MAIN EXPLOSIVE BUTTON CLASS ===== */
.book-button-explosive {
  position: relative;
  /* background: linear-gradient(135deg, #f97316, #ef4444, #ec4899, #a855f7); */
  background-size: 300% 300%;
  border: 3px solid rgba(249, 115, 22, 0.6);
  color: white !important;
  font-weight: 900 !important;
  letter-spacing: 2px;
  text-transform: uppercase;
  cursor: pointer;
  /* animation: backgroundWaves 3s ease-in-out infinite, buttonGlowExplosive 2s ease-in-out infinite; */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(249,115,22,.6);
}

.book-button-explosive:hover {
  transform: scale(1.15) translateY(-8px) rotate(2deg);
  animation: backgroundWaves 1.5s ease-in-out infinite, buttonGlowExplosive 1s ease-in-out infinite;
}

/* ===== SUB-COMPONENTS ===== */
.background-waves {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: linear-gradient(135deg, #f97316, #ef4444, #ec4899, #a855f7, #3b82f6); */
  background-size: 400% 400%;
  border-radius: inherit;
  animation: backgroundWaves 4s ease-in-out infinite;
  z-index: 1;
}

.shimmer-effect-explosive {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.8), 
    rgba(249, 115, 22, 0.6),
    rgba(255, 255, 255, 0.9),
    transparent
  );
  border-radius: inherit;
  animation: shimmerExplosive 2s infinite;
  z-index: 2;
}

.light-rays {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  z-index: 3;
}

.ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 80px;
  background: linear-gradient(to top, transparent, rgba(255, 255, 255, 0.8), transparent);
  transform-origin: bottom;
  animation: lightRay 2s ease-in-out infinite;
}

.ray-1 { --rotation: 0deg; animation-delay: 0s; }
.ray-2 { --rotation: 60deg; animation-delay: 0.3s; }
.ray-3 { --rotation: 120deg; animation-delay: 0.6s; }
.ray-4 { --rotation: 180deg; animation-delay: 0.9s; }
.ray-5 { --rotation: 240deg; animation-delay: 1.2s; }
.ray-6 { --rotation: 300deg; animation-delay: 1.5s; }

.particles-explosion {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  z-index: 5;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 1), rgba(249, 115, 22, 0.8));
  border-radius: 50%;
  animation: particleExplosion 3s ease-out infinite;
}

.particle-1 { --dx: 40px; --dy: -30px; animation-delay: 0s; }
.particle-2 { --dx: -35px; --dy: -25px; animation-delay: 0.2s; }
.particle-3 { --dx: 30px; --dy: 35px; animation-delay: 0.4s; }
.particle-4 { --dx: -40px; --dy: 30px; animation-delay: 0.6s; }
.particle-5 { --dx: 45px; --dy: 0px; animation-delay: 0.8s; }
.particle-6 { --dx: -30px; --dy: 0px; animation-delay: 1s; }
.particle-7 { --dx: 0px; --dy: -40px; animation-delay: 1.2s; }
.particle-8 { --dx: 0px; --dy: 40px; animation-delay: 1.4s; }

/* ===== STAR ANIMATIONS ===== */
.star-explosion {
  animation: starExplosion 3s ease-in-out infinite;
}

.star-burst-explosive {
  animation: starBurstExplosive 2.5s ease-in-out infinite;
}

.star-sparkle {
  animation: starSparkle 2s ease-in-out infinite;
}

/* ===== TEXT AND ICON EFFECTS ===== */
.text-explosive {
  animation: textExplosive 2s ease-in-out infinite;
  color: white !important;
  position: relative;
  z-index: 50;
}

.book-button-explosive .icon-explosive {
  animation: iconExplosive 2.5s ease-in-out infinite;
  color: white !important;
}

/* Star Animation Classes */
.star-pulse {
  animation: starPulse 1.5s ease-in-out infinite;
}

.star-burst {
  animation: starBurst 2s ease-in-out infinite;
}

.star-twinkle {
  animation: starTwinkle 2.5s ease-in-out infinite;
}

/* Enhanced Button Icon Animations cho button mới */
.book-button-explosive svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white !important;
  z-index: 50;
  position: relative;
}

.book-button-explosive:hover svg {
  animation: iconExplosive 1.5s ease-in-out infinite;
}

.book-button-explosive:hover .text-explosive {
  animation: textExplosive 1.5s ease-in-out infinite;
}

.book-button-explosive:hover .star-explosion {
  animation: starExplosion 2s ease-in-out infinite;
}

.book-button-explosive:hover .star-burst-explosive {
  animation: starBurstExplosive 1.8s ease-in-out infinite;
}

.book-button-explosive:hover .star-sparkle {
  animation: starSparkle 1.5s ease-in-out infinite;
}

/* Star colors for border button */
.book-button-enhanced .star-pulse {
  color: #f59e0b !important;
}

.book-button-enhanced .star-burst {
  color: #f97316 !important;
}

.book-button-enhanced .star-twinkle {
  color: #ef4444 !important;
}

.book-button-enhanced:hover .star-pulse {
  color: #fbbf24 !important;
}

.book-button-enhanced:hover .star-burst {
  color: #fb923c !important;
}

.book-button-enhanced:hover .star-twinkle {
  color: #f87171 !important;
}

/* Float Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Fade in Animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}
