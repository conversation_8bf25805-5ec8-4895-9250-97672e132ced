.nav-align-top .nav-tabs~.tab-content {
    box-shadow: none !important;
}

.container-xxl.container-p-y.h-full.p-top0 {
    padding: 0 !important;
}

.text-danger.field-validation-error {
    display: none;
}

#group-query {
    display: flex;
    flex-direction: row;
    justify-content: end;
    margin-bottom: -4px;
}

#group-query .length-table label {
    display: flex;
    justify-content: center;
    align-items: center;
}

#group-query .length-table label select {
    margin: 0 8px;
}

#group-query .input-query {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
}

#group-query .form-group {
    margin-right: 16px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background: #696cff;
    color: white;
    font-weight: bold;
    padding: 10px;
    border-color: gray;
}

th {
    padding: 12px 0;
    text-align: center;
}

tr {
    height: 30px;
}

th i {
    padding-left: 8px;
}

th i:hover {
    cursor: pointer;
    color: #cebfbf;
}

table tbody tr:nth-child(even) {
    background-color: #f2f2f2;
}

table tbody tr:nth-child(odd) {
    background-color: #ffffff;
}

tbody tr td.data-not-found {
    text-align: center;
    background: #e7f2ea;
}


#navs-top-home nav {
    position: relative !important;
    bottom: 5px !important;
}

.group-quickly {
    display: none;
    justify-content: end;
    flex-direction: row;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 -2px 6px 0 rgba(0, 0, 0, .12);
    padding: 16px 65px;
    z-index: 10;
    align-items: center;
}

.group-quickly .group-quickly-right {
    display: flex;
    justify-content: flex-end;
    align-items: baseline;
}

.group-quickly .group-quickly-right button {
    margin-left: 20px;
}

.h-full {
    height: 100% !important;
}

.p-top0 {
    padding-top: 0 !important;
}

.group-button-table {
    display: flex;
    justify-content: space-between;
}
.pagination{
    margin-top: 16px;
    justify-content: right;
    width: 100%;
}

/*details*/
#navs-detail .row .left,
#navs-detail .row .right {
    padding: 0px 30px 10px 30px;
    border: 1px solid silver;
    border-radius: 8px;
    margin: 0 10px;
}

#navs-detail .row .left::before {
    content: "Detail";
    position: relative;
    top: -12px;
    left: -17px;
    background-color: #fff;
    padding: 0 5px;
    font-weight: bold;
}

#navs-detail .row .right::before {
    content: "Settings";
    position: relative;
    top: -12px;
    left: -17px;
    background-color: #fff;
    padding: 0 5px;
    font-weight: bold;
}


@media (min-width: 992px) {
    .col-lg-6.left, .col-lg-6.right {
        width: calc(50% - 20px);
    }
}

#navs-detail .row .right .row .col-12 {
    display: flex;
    justify-content: center;
}

#navs-detail .row .right .row img {
    width: 100px;
    min-height: 100px;
    height: auto;
    margin-bottom: 16px;
    border: 1px solid silver;
    border-radius: 8px;
}

.custom-file-upload input[type="file"], .custom-file-upload-edit input[type="file"] {
    display: none;
}
.form-control {
    min-width: 100% !important;
}


#img-selected div {
    width: auto;
    height: 91px;
    margin-right: 8px;
    margin-bottom: 18px;
    min-width: 91px;
}

#img-selected div:hover i {
    display: flex;
}

#img-selected div img {
    width: 100px !important;
    height: 100px !important;
    padding: 0 !important;
    margin: 0 !important;
    object-fit: cover; 
    object-position: center center; 
}

#img-selected div i {
    position: relative;
    bottom: 24px;
    justify-content: center;
    background: gray;
    padding: 4px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    color: #fff;
    cursor: pointer;
    display: none;
}

#img-selected div i::before {
    cursor: pointer;
}

.custom-file-upload, .custom-file-upload-edit {
    padding: 5px;
    width: 91px;
    height: 91px;
    background-color: #ee4d2d17;
    color: #ee4d2d;
    cursor: pointer;
    border-radius: 4px;
    border: 1px dashed #d8d8d8;
    justify-content: center;
    align-items: center;
    display: flex;
    flex-direction: column;
}

.custom-file-upload i, .custom-file-upload-edit i {
    font-size: x-large !important;
}

.custom-file-upload span, .custom-file-upload-edit span {
    font-size: small !important;
}

.custom-file-upload:hover, .custom-file-upload-edit:hover {
    background: rgba(238, 77, 45, .03);
    border-color: #ee4d2d;
}

.form-group {
    margin-bottom: 8px;
}

.custom-file-upload-label {
    display: block;
    text-align: center;
}

.col-form-label {
    padding-bottom: 0;
}

.col-form-label .form-label {
    margin-bottom: 0;
}

#navs-detail .row .col-12.group-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: flex-end;
    background: white;
    box-shadow: 0 -2px 6px 0 rgba(0, 0, 0, .12);
    padding: 16px 65px;
    z-index: 100;
}

#navs-detail .row .col-12.group-bottom button {
    margin-left: 16px !important;
    min-width: 100px;
}

#img-selected {
    display: flex;
    flex-wrap: wrap;
}

.hidden {
    display: none !important;
}

.price-froup {
    width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
}


.note-modal-backdrop {
    display: none !important;
    z-index: -1 !important;
}

.note-modal-footer {
    height: 53px;
}

th:nth-child(1) {
    border-top-left-radius: 8px;
}
table th:last-child{
    border-top-right-radius: 8px;
    min-width: 20px;
}

td:nth-child(1),
td:nth-child(7) {
    text-align: center;
}

#table-news-inactive td:nth-child(1),
#table-category-product td:nth-child(2) {
    text-align: center;
}

table tbody tr:hover {
    background-color: #716dff1f;
    color: #333;
    padding: 10px;
    cursor: pointer; 
}

table td {
    padding: 8px;
}

td img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 5%;
    background: #f1f1f1;
}
.nav.nav-tabs{
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}