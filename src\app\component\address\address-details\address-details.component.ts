import { Component, ElementRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { QuillEditorComponent, QuillViewHTMLComponent } from 'ngx-quill';
import { AddressCommonComponent } from '../address-common/address-common.component';
import { NMFlightSearchBoxComponent } from '../../common/nmflight-search-box/nmflight-search-box.component';
import { DomSanitizer, SafeHtml, Title } from '@angular/platform-browser';
import { AddressService } from '../../../service/address/address.service';
import { ToursService } from '../../../service/tour/tour.service';
import { ScreenService } from '../../../service/screen/screen.service';
import { TranslateService } from '@ngx-translate/core';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';


@Component({
  selector: 'app-address-details',
  imports: [
    AddressCommonComponent,
    QuillEditorComponent,
    ReactiveFormsModule
  ],
  templateUrl: './address-details.component.html',
  styleUrl: './address-details.component.css'
})
export class AddressDetailsComponent {
  formContentShow: any;
  tours: any = [];
  addressDetails: any;
  addressContent: SafeHtml | null = null;
  @ViewChild('containerContent') containerContent: ElementRef | null = null;
  @ViewChild('btnShowMore') btnShowMore: ElementRef | null = null;
  seoTitle: string = '';
  quillModules = { toolbar: false };

  constructor(
    private addressService: AddressService,
    private route: ActivatedRoute,
    private tourService: ToursService,
    private titleService: Title,
    private translate: TranslateService,
    private router: Router,
    private formBuilder: FormBuilder

  ) {

  }

  ngOnInit(): void {
    const lang = localStorage.getItem('lang');
    this.translate.use(lang || 'vi');

    this.route.paramMap.subscribe(params => {
      this.seoTitle = window.location.href.split('/').reverse()[0];
      this.loadDetailsAddress(this.seoTitle, this.translate.currentLang);
    });

    this.translate.onLangChange.subscribe(() => {
      const translatedSeoTitle = this.addressDetails?.diffSeoTitle;
      if (translatedSeoTitle) {
        this.router.navigate([`/address/${translatedSeoTitle}`]);
        // this.loadDetailsAddress(translatedSeoTitle, this.translate.currentLang);
      }
    });
  }

  ngAfterViewInit(): void {
    this.loadHotTour();
  }

  loadDetailsAddress(seoTitle: string, lang: string) {
    this.addressService.getByTitle(seoTitle, lang).subscribe((data: any) => {
      this.titleService.setTitle(data.resultObj?.name + ' | Ngoc Mai Travel');
      this.addressDetails = data.resultObj;
      this.formContentShow = this.formBuilder.group({
        Detail: [this.addressDetails?.content],
      });
      if (this.containerContent && this.btnShowMore) {
        this.containerContent.nativeElement.classList.add('limit-height');
        this.btnShowMore.nativeElement.style.display = 'block';
      }
    });
  }

  loadHotTour() {
    this.tourService.getTops('all', 5).subscribe((res) => {
      this.tours = res.resultObj;
    });
  }

  showMore() {
    if (this.containerContent && this.btnShowMore) {
      this.containerContent.nativeElement.classList.remove('limit-height');
      this.btnShowMore.nativeElement.style.display = 'none';
    }
  }

  formatDate(date: Date): string | null {
    var date1 = new Date(date);
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric'
    }).format(date1);
  }
}