<div class="m-4 max-w-full">
    <div class="w-full h-full relative flex flex-col pb-4 sm:rounded-lg md:gap-6 gap-2">
        <!-- Search Header with Toggle Button -->
        <div class="w-full bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 shadow-md rounded-xl border border-gray-200 dark:border-gray-600">
            <!-- Always visible header -->
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Tì<PERSON> kiếm & <PERSON></h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Tìm kiếm đơn hàng và vé máy bay</p>
                    </div>
                </div>
                
                <!-- Quick search and toggle button -->
                <div class="flex items-center gap-2">
                    <!-- Quick search input (always visible) -->
                    <div class="relative">
                        <input type="search" [(ngModel)]="searchModel.CodeKeyword"
                            class="pl-9 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg w-64 bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-white"
                            placeholder="Mã đơn hàng hoặc tên khách...">
                        <svg class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    
                    <!-- Quick search button -->
                    <button type="button" (click)="onSubmitSearch()"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </button>
                    
                    <!-- Toggle advanced filters button -->
                    <button type="button" (click)="toggleAdvancedSearch()" 
                        class="flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                        [class.bg-blue-50]="showAdvancedSearch"
                        [class.border-blue-300]="showAdvancedSearch">
                        <svg class="w-4 h-4 transition-transform duration-200" [class.rotate-180]="showAdvancedSearch" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                        <span class="text-sm font-medium">{{showAdvancedSearch ? 'Ẩn bộ lọc' : 'Bộ lọc nâng cao'}}</span>
                    </button>
                </div>
            </div>
            
            <!-- Advanced search panel (collapsible) -->
            <div class="transition-all duration-300 ease-in-out overflow-hidden" 
                 [class.max-h-0]="!showAdvancedSearch" 
                 [class.max-h-96]="showAdvancedSearch"
                 [class.opacity-0]="!showAdvancedSearch"
                 [class.opacity-100]="showAdvancedSearch">
                <div class="border-t border-gray-200 dark:border-gray-600 p-4 space-y-4 bg-white dark:bg-gray-800 rounded-b-xl">
                    
                    <!-- Date filters row -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Loại ngày</label>
                            <select [(ngModel)]="searchModel.TypeDate"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="BookingDate">Ngày đặt</option>
                                <option value="DepartDate">Ngày bay</option>
                            </select>
                        </div>
                        
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Từ ngày</label>
                            <div class="relative">
                                <input #dpFromDate readonly
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                                    placeholder="dd/MM/yyyy" 
                                    [value]="formatDateTo_ddMMyyyy(fromDate)"
                                    (click)="fromDatepicker.toggle()"/>
                                <svg class="w-4 h-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer" 
                                     (click)="fromDatepicker.toggle()" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <div class="dp-hidden absolute z-50">
                                <input name="fromDatepicker" class="form-control" ngbDatepicker
                                    #fromDatepicker="ngbDatepicker" [autoClose]="'outside'"
                                    (dateSelect)="onFromDateSelect($event)" [displayMonths]="isMobile ? 1 : 2"
                                    [dayTemplate]="t" outsideDays="hidden" tabindex="-1" />
                                <ng-template #t let-date let-focused="focused">
                                    <span class="custom-day" [class.focused]="focused"
                                        [class.selected]="date.equals(fromDate)" (mouseenter)="hoveredDate = date"
                                        (mouseleave)="hoveredDate = null">
                                        {{ date.day }}
                                    </span>
                                </ng-template>
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Đến ngày</label>
                            <div class="relative">
                                <input #dpToDate readonly
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                                    placeholder="dd/MM/yyyy" 
                                    [value]="formatDateTo_ddMMyyyy(toDate)"
                                    (click)="toDatepicker.toggle()"/>
                                <svg class="w-4 h-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer" 
                                     (click)="toDatepicker.toggle()" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <div class="dp-hidden absolute z-50">
                                <input name="toDatepicker" class="form-control" ngbDatepicker
                                    #toDatepicker="ngbDatepicker" [autoClose]="'outside'"
                                    (dateSelect)="onToDateSelect($event)" [displayMonths]="isMobile ? 1 : 2"
                                    [dayTemplate]="toDateTemplate" outsideDays="hidden" tabindex="-1" />
                                <ng-template #toDateTemplate let-date let-focused="focused">
                                    <span class="custom-day" [class.focused]="focused"
                                        [class.selected]="date.equals(toDate)" (mouseenter)="hoveredDate = date"
                                        (mouseleave)="hoveredDate = null">
                                        {{ date.day }}
                                    </span>
                                </ng-template>
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Số dòng hiển thị</label>
                            <select [(ngModel)]="searchModel.PageSize" (change)="changePageSize($event)"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="10">10 dòng</option>
                                <option value="20">20 dòng</option>
                                <option value="50">50 dòng</option>
                                <option value="100">100 dòng</option>
                                <option value="200">200 dòng</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Search inputs row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Mã đơn hàng / Mã đặt chỗ</label>
                            <input type="search" [(ngModel)]="searchModel.CodeKeyword"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Nhập mã đơn hàng hoặc mã đặt chỗ...">
                        </div>
                        
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Thông tin khách hàng</label>
                            <input type="search" [(ngModel)]="searchModel.CustomerKeyword"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="Họ tên, email hoặc số điện thoại...">
                        </div>
                    </div>
                    
                    <!-- Filter buttons row -->
                    <div class="flex flex-wrap items-center justify-between gap-4 pt-2 border-t border-gray-100 dark:border-gray-700">
                        <div class="flex flex-wrap gap-2">
                            <!-- Status filter dropdown -->
                            <div ngbDropdown class="relative">
                                <button ngbDropdownToggle
                                    class="inline-flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                                    </svg>
                                    <span class="text-sm">Trạng thái: {{selectedTypeFilter}}</span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div ngbDropdownMenu class="z-50 hidden">
                                    <div class="bg-white dark:bg-gray-700 min-w-[8rem] max-h-64 overflow-y-auto rounded-md border shadow-md p-1">
                                        @for (type of typeFilter; track $index) {
                                        <button type="button" ngbDropdownItem (click)="setSelectedTypeFilter(type.key)"
                                            class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors">
                                            {{type.value}}
                                        </button>
                                        }
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Agent filter dropdown -->
                            <div ngbDropdown class="relative">
                                <button ngbDropdownToggle
                                    class="inline-flex items-center gap-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 21v-2a4 4 0 00-4-4H6a4 4 0 00-4 4v2M9 7a4 4 0 108 0M22 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75"/>
                                    </svg>
                                    <span class="text-sm">Đại lý: {{selectedAgentFilter}}</span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div ngbDropdownMenu class="z-50 hidden">
                                    <div class="bg-white dark:bg-gray-700 min-w-[8rem] max-h-64 overflow-y-auto rounded-md border shadow-md p-1">
                                        @for (agent of agents; track $index) {
                                        <button type="button" ngbDropdownItem (click)="setSelectedAgentFilter(agent?.value)"
                                            class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors">
                                            {{agent?.name}}
                                        </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action buttons -->
                        <div class="flex gap-2">
                            <button type="button" (click)="resetSearch()" 
                                class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                </svg>
                                Đặt lại
                            </button>
                            <button type="button" (click)="onSubmitSearch()"
                                class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                                Tìm kiếm
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="w-full bg-white dark:bg-gray-800 shadow-md rounded-xl border border-gray-200 dark:border-gray-600 overflow-hidden">
            <!-- Table header with total count -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Danh sách đơn hàng</h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Tổng số: {{dataTable.totalRecords}} đơn hàng
                        @if (dataTable.totalRecords > 0) {
                            (Hiển thị {{dataTable.from}} - {{dataTable.to}})
                        }
                    </p>
                </div>
                
                <!-- Export/Action buttons -->
                <div class="flex gap-2">
                    <button type="button" class="px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Xuất Excel
                    </button>
                </div>
            </div>

            <!-- Table container with scroll -->
            <div class="overflow-x-auto max-h-[calc(100vh-280px)] min-h-[400px]">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400 sticky top-0">
                        <tr>
                            <th scope="col" class="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" (click)="sortTable('id')">
                                <div class="flex items-center gap-1">
                                    <span>Mã đơn hàng</span>
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"/>
                                    </svg>
                                </div>
                            </th>
                            <th scope="col" class="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" (click)="sortTable('customerName')">
                                <div class="flex items-center gap-1">
                                    <span>Khách hàng</span>
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"/>
                                    </svg>
                                </div>
                            </th>
                            <th scope="col" class="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" (click)="sortTable('bookingDate')">
                                <div class="flex items-center gap-1">
                                    <span>Ngày đặt</span>
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"/>
                                    </svg>
                                </div>
                            </th>
                            <th scope="col" class="px-4 py-3">Chuyến bay</th>
                            <th scope="col" class="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" (click)="sortTable('totalPrice')">
                                <div class="flex items-center gap-1">
                                    <span>Tổng tiền</span>
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"/>
                                    </svg>
                                </div>
                            </th>
                            <th scope="col" class="px-4 py-3">Trạng thái</th>
                            <th scope="col" class="px-4 py-3">Đại lý</th>
                            <th scope="col" class="px-4 py-3">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (dataTable.items.length === 0) {
                            <tr>
                                <td colspan="8" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                                    <div class="flex flex-col items-center gap-2">
                                        <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                        <p class="font-medium">Không tìm thấy dữ liệu</p>
                                        <p class="text-sm">Thử thay đổi các tiêu chí tìm kiếm</p>
                                    </div>
                                </td>
                            </tr>
                        } @else {
                            @for (item of dataTable.items; track $index) {
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors"
                                    (dblclick)="handleDoubleClick(item)">
                                    <td class="px-4 py-3 font-medium text-gray-900 dark:text-white">
                                        <div class="flex flex-col">
                                            <span class="font-semibold">{{item?.code}}</span>
                                            @if (item?.bookingCode) {
                                                <span class="text-xs text-gray-500">PNR: {{item?.bookingCode}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col">
                                            <span class="font-medium">{{item?.customerName}}</span>
                                            @if (item?.customerEmail) {
                                                <span class="text-xs text-gray-500">{{item?.customerEmail}}</span>
                                            }
                                            @if (item?.customerPhone) {
                                                <span class="text-xs text-gray-500">{{item?.customerPhone}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col">
                                            <span>{{formatDate(item?.bookingDate)}}</span>
                                            <span class="text-xs text-gray-500">{{formatTime(item?.bookingDate)}}</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col gap-1">
                                            @if (item?.departure && item?.destination) {
                                                <div class="flex items-center gap-1 text-sm">
                                                    <span class="font-medium">{{item?.departure}}</span>
                                                    <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                                    </svg>
                                                    <span class="font-medium">{{item?.destination}}</span>
                                                </div>
                                            }
                                            @if (item?.departureDate) {
                                                <span class="text-xs text-gray-500">{{formatDate(item?.departureDate)}}</span>
                                            }
                                            @if (item?.airline) {
                                                <span class="text-xs text-blue-600">{{item?.airline}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col">
                                            <span class="font-semibold text-blue-600">{{formatCurrency(item?.totalPrice)}}</span>
                                            @if (item?.paidAmount && item?.paidAmount > 0) {
                                                <span class="text-xs text-green-600">Đã thanh toán: {{formatCurrency(item?.paidAmount)}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        @switch (item?.status) {
                                            @case (0) {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Đơn mới
                                                </span>
                                            }
                                            @case (1) {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Đã thanh toán
                                                </span>
                                            }
                                            @case (2) {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    Đang thực hiện
                                                </span>
                                            }
                                            @case (-1) {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    Đóng
                                                </span>
                                            }
                                            @default {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    {{item?.statusText}}
                                                </span>
                                            }
                                        }
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col">
                                            <span class="text-sm">{{item?.agentName}}</span>
                                            @if (item?.agentCode) {
                                                <span class="text-xs text-gray-500">{{item?.agentCode}}</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex gap-1">
                                            <button type="button" (click)="viewDetails(item)" 
                                                class="p-1.5 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors" 
                                                title="Xem chi tiết">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                </svg>
                                            </button>
                                            @if (item?.status === 0 || item?.status === 2) {
                                                <button type="button" (click)="editItem(item)" 
                                                    class="p-1.5 text-orange-600 hover:bg-orange-100 rounded-lg transition-colors" 
                                                    title="Chỉnh sửa">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                                    </svg>
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (dataTable.totalRecords > 0) {
                <div class="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-600">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        Hiển thị {{dataTable.from}} - {{dataTable.to}} trong tổng số {{dataTable.totalRecords}} kết quả
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <!-- Previous page -->
                        <button type="button" 
                            [disabled]="searchModel.PageIndex <= 1"
                            (click)="changePage(searchModel.PageIndex - 1)"
                            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        
                        <!-- Page numbers -->
                        @for (page of range(1, dataTable.pageCount); track page) {
                            <button type="button" 
                                (click)="changePage(page)"
                                [class.bg-blue-600]="page === searchModel.PageIndex"
                                [class.text-white]="page === searchModel.PageIndex"
                                [class.border-blue-600]="page === searchModel.PageIndex"
                                class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                                {{page}}
                            </button>
                        }
                        
                        <!-- Next page -->
                        <button type="button" 
                            [disabled]="searchModel.PageIndex >= dataTable.pageCount"
                            (click)="changePage(searchModel.PageIndex + 1)"
                            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
