<div class="relative address-details pt-24">
    <div class="relative px-[10%] max-md:px-2 z-20">
        @if(addressDetails){
        <div>
            <h3 class="py-6 text-[#0f294d] text-3xl font-extrabold text-center">{{addressDetails?.name}} </h3>
            <div class="rounded-lg shadow p-8 max-md:p-4 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                <div class="relative w-full">
                    <div #containerContent class="limit-height">
                        <div>
                            <!-- <quill-view-html class="mobile-preview-content dark:text-gray-200"
                                [content]="addressDetails?.content"></quill-view-html> -->

                            @if(formContentShow){
                            <form [formGroup]="formContentShow">
                                <quill-editor formControlName="Detail" [modules]="quillModules"
                                    [readOnly]="true"></quill-editor>
                            </form>
                            }
                        </div>
                    </div>
                    <button #btnShowMore
                        class="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-white bg-primary-600 hover:bg-primary-600/80 w-fit focus:ring-4 focus:outline-none focus:ring-primary-600/50 font-medium rounded-full text-sm px-5 py-2.5 text-center whitespace-nowrap flex items-center dark:hover:bg-primary-600/80 dark:focus:ring-primary-600/40"
                        (click)="showMore()">
                        <span class="inline-flex justify-center items-center hover:gap-2">
                            Xem thêm
                            <svg class="w-6 h-6 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m19 9-7 7-7-7" />
                            </svg>
                        </span>
                    </button>

                </div>
            </div>
        </div>
        }@else {
        <div class="flex flex-col items-center justify-center min-h-screen bg-gray-100">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-800">Không tìm thấy nội dung</h1>
                <p class="mt-4 text-lg text-gray-600">
                    Rất tiếc, chúng tôi không thể tìm thấy bài viết bạn đang tìm kiếm. Vui lòng kiểm tra lại URL hoặc
                    thử tìm kiếm khác.
                </p>
                <div class="mt-6">
                    <a href="/"
                        class="px-6 py-3 text-white bg-primary-600 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring focus:ring-primary-300">
                        Trở về trang chủ
                    </a>
                </div>
            </div>
        </div>

        }

        <!-- <div class="flex justify-center items-center w-full">
            <app-nmflight-search-box></app-nmflight-search-box>
        </div> -->
    </div>
</div>
<app-address-common></app-address-common>
<div class="mt-10"></div>