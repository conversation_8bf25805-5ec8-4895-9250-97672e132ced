<div class="min-h-screen relative md:p-6 p-2 overflow-hidden">
    <!-- Over<PERSON> n<PERSON><PERSON> sang trọng -->
    <div class="absolute inset-0 z-0">
        <div class="stars"></div>
        <div class="stars2"></div>
        <div class="stars3"></div>
        <div class="w-full h-full bg-gradient-to-br from-[#181818] via-[#232946] to-[#1a1a2e] absolute inset-0"></div>
        <!-- Hiệu ứng ánh sáng tím xanh -->
        <div
            class="absolute left-[-10%] top-[-10%] w-[60vw] h-[60vw] bg-gradient-radial from-[#7f5af0cc] via-transparent to-transparent opacity-30 blur-3xl">
        </div>
        <div
            class="absolute right-[-15%] bottom-[-10%] w-[50vw] h-[50vw] bg-gradient-radial from-[#ffd70099] via-transparent to-transparent opacity-20 blur-2xl">
        </div>
        <!-- Pattern lưới mờ -->
        <div class="absolute inset-0 bg-[url('/assets/img/patterns/luxury-grid.svg')] opacity-10 mix-blend-overlay">
        </div>
    </div>
    <div class="max-w-6xl mx-auto relative z-10">
        <div class="text-center mb-12">
            <div class="relative inline-block mb-8 mt-6">
                <div
                    class="absolute -inset-2 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 rounded-full blur-xl animate-pulse opacity-70">
                </div>
                <h1
                    class="relative px-6 md:px-10 py-4 md:py-5 rounded-full bg-black/30 backdrop-blur-xl shadow-2xl flex items-center gap-3 md:gap-4 border-2 border-amber-400/60">
                    <svg class="w-8 h-8 md:w-10 md:h-10 text-amber-300 drop-shadow-lg shrink-0 floating" fill="none"
                        stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path
                            d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z">
                        </path>
                        <path d="M5 21h14"></path>
                    </svg>
                    <span
                        class="text-2xl md:text-3xl lg:text-4xl font-extrabold font-serif bg-gradient-to-r from-yellow-300 via-amber-200 to-yellow-300 bg-clip-text text-transparent tracking-wide drop-shadow-[0_2px_15px_#fcd34d99]">
                        Thẻ BSL Business Lounge Access
                    </span>
                    <svg class="w-8 h-8 md:w-10 md:h-10 text-amber-300 drop-shadow-lg shrink-0 floating-slow"
                        fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path
                            d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z">
                        </path>
                        <path d="M5 21h14"></path>
                    </svg>
                </h1>
            </div>
            <p class="text-base sm:text-lg text-gray-200  mx-auto font-light">Trải nghiệm dịch vụ hạng thương
                gia
                tại các
                sân bay với ưu đãi đặc biệt dành riêng cho thành viên</p>
        </div>
        <div
            class="mb-16 max-md:px-2 grid md:grid-cols-2 grid-cols-1 md:px-12 md:gap-x-12 gap-y-6 bg-white/10 backdrop-blur-md py-6 rounded-2xl shadow-2xl border border-yellow-400/30 relative overflow-hidden animate-entry">
            <div
                class="absolute inset-0 bg-gradient-to-r from-yellow-200/10 via-yellow-400/10 to-yellow-200/10 animate-pulse">
            </div>
            <div
                class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-yellow-400/30 to-yellow-600/30 rounded-bl-full blur-2xl">
            </div>
            <div
                class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-600/30 to-yellow-400/30 rounded-tr-full blur-2xl">
            </div>


            <div class="md:flex flex-col group relative hidden">
                <img alt="BSL Membership Cards" loading="lazy" width="1200" height="400" decoding="async" data-nimg="1"
                    class="w-full max-w-4xl mx-auto group-hover:scale-110 transition-all duration-300 relative z-10 rounded-xl border-4 border-yellow-400 shadow-xl"
                    src="/assets/img/service/airport-lounge/BSL/gold.jpg" style="color: transparent;">
                <span
                    class="text-lg font-semibold text-center md:mt-6 mt-2 text-yellow-200 group-hover:text-yellow-400 group-hover:font-sans relative z-10 drop-shadow-lg">
                    Thẻ BSL Gold
                </span>
            </div>
            <div class="md:flex flex-col group relative hidden ">
                <img alt="BSL Membership Cards" loading="lazy" width="1200" height="400" decoding="async" data-nimg="1"
                    class="w-full max-w-4xl mx-auto group-hover:scale-110 transition-all duration-300 relative z-10 rounded-xl border-4 border-yellow-400 shadow-xl"
                    src="/assets/img/service/airport-lounge/BSL/diamond.jpg" style="color: transparent;">
                <span
                    class="text-lg font-semibold text-center md:mt-6 mt-2 text-yellow-300 group-hover:text-yellow-400 group-hover:font-sans relative z-10 drop-shadow-lg">
                    Thẻ BSL Diamond
                </span>
            </div>
            <div class="flex flex-col group relative md:hidden">
                <img alt="BSL Membership Cards" loading="lazy" width="1200" height="400" decoding="async" data-nimg="1"
                    class="w-full max-w-4xl mx-auto group-hover:scale-110 transition-all duration-300 relative z-10 rounded-xl border-4 border-yellow-400 shadow-xl"
                    src="/assets/img/service/airport-lounge/BSL/both.webp" style="color: transparent;">
                <span
                    class="text-lg font-semibold text-center md:mt-6 mt-2 text-yellow-200 group-hover:text-yellow-400 group-hover:font-sans relative z-10 drop-shadow-lg">
                    Thẻ BSL Diamond & Gold
                </span>
            </div>
        </div>
        <div class="grid lg:grid-cols-2 gap-8 mb-12 animate-entry">
            <div
                class="card-hover group rounded-lg bg-gradient-to-br from-gray-900 via-[#1a1a2e] to-gray-900 backdrop-blur-md text-card-foreground relative overflow-hidden border border-purple-400/40 shadow-2xl transition-all duration-500 hover:shadow-[0_0_40px_8px] hover:shadow-purple-500/20 hover:-translate-y-2">
                <div
                    class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500 to-violet-600 rounded-bl-full opacity-20 group-hover:opacity-40 transition-opacity duration-500">
                </div>
                <div class="flex flex-col space-y-1.5 p-4 sm:px-6 pt-6 pb-0 relative">
                    <div class="w-full flex flex-col items-center justify-center mb-6">
                        <div
                            class="inline-flex items-center px-5 py-3 sm:px-7 sm:py-4 rounded-full bg-black/30 backdrop-blur-lg border-2 border-purple-400/70 shadow-xl gap-3 luxury-title-box">
                            <!-- <svg class="w-9 h-9 text-purple-300 drop-shadow-lg transition-transform duration-500 group-hover:scale-110"
                                fill="none" stroke="currentColor" stroke-width="2.2" viewBox="0 0 24 24">
                                <path
                                    d="M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z">
                                </path>
                            </svg> -->
                            <span
                                class="text-xl sm:text-4xl font-extrabold font-serif bg-gradient-to-r from-purple-200 via-violet-300 to-purple-200 bg-clip-text text-transparent tracking-widest drop-shadow-[0_2px_24px_#c084fc99] luxury-title-glow group-hover:drop-shadow-[0_2px_32px_#c084fccc] transition-all duration-500">
                                BSL Gold
                            </span>
                            <!-- <svg class="w-9 h-9 text-purple-300 drop-shadow-lg transition-transform duration-500 group-hover:scale-110"
                                fill="none" stroke="currentColor" stroke-width="2.2" viewBox="0 0 24 24">
                                <path
                                    d="M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z">
                                </path>
                            </svg> -->
                        </div>
                        <span class="block text-lg md:text-xl font-light text-purple-200 mt-2 tracking-wide">Giải pháp
                            tối ưu cho hành trình nội địa</span>
                    </div>
                </div>
                <div class="p-4 sm:p-6 pt-0 space-y-6">
                    <div class="grid gap-3">
                        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-3 border border-purple-300/50">
                            <div class="flex justify-between items-center "><span
                                    class="font-semibold text-purple-200">Thẻ 5 lượt:</span>
                                <div class="font-bold text-base sm:text-lg text-white">1,650,000


                                </div>
                            </div>
                        </div>
                        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-3 border border-purple-300/50">
                            <div class="flex justify-between items-center "><span
                                    class="font-semibold text-purple-200">Thẻ 10 lượt:</span>
                                <div class="font-bold text-base sm:text-lg text-white">3,200,000


                                </div>
                            </div>
                        </div>
                        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-3 border border-purple-300/50">
                            <div class="flex justify-between items-center "><span
                                    class="font-semibold text-purple-200">Thẻ 20 lượt:</span>
                                <div class="font-bold text-base sm:text-lg text-white">6,100,000


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-4 sm:p-6 pt-0 space-y-4">
                    <div class="flex items-start gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-check-circle w-5 h-5 text-purple-300 flex-shrink-0 mt-1">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        <p class="text-purple-200 text-sm sm:text-base">Áp dụng tại tất cả phòng khách nội địa (trừ Hà
                            Nội,
                            Đà Nẵng, TP.HCM)
                        </p>
                    </div>
                    <div class="flex items-start gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-check-circle w-5 h-5 text-purple-300 flex-shrink-0 mt-1">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        <p class="text-purple-200 text-sm sm:text-base">Chi phí/lượt thấp hơn so với mua lẻ</p>
                    </div>
                    <div class="flex items-start gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-check-circle w-5 h-5 text-purple-300 flex-shrink-0 mt-1">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        <p class="text-purple-200 text-sm sm:text-base">Phù hợp cho doanh nhân, nhân sự công tác, nhóm
                            khách nội địa</p>
                    </div>
                </div>
            </div>
            <div
                class="card-hover group rounded-lg bg-gradient-to-br from-gray-900 via-[#2c2013] to-gray-900 backdrop-blur-md text-card-foreground relative overflow-hidden border border-amber-500/40 shadow-2xl transition-all duration-500 hover:shadow-[0_0_40px_8px] hover:shadow-amber-500/20 hover:-translate-y-2">
                <!-- <div
                    class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-bl-full opacity-20 group-hover:opacity-40 transition-opacity duration-500">
                </div> -->
                <div class="flex flex-col space-y-1.5 p-4 sm:px-6 pt-6 pb-0 relative">
                    <div class="w-full flex flex-col items-center justify-center mb-6">
                        <!-- <span
                            class="inline-block text-xs font-bold tracking-widest text-amber-800 bg-gradient-to-r from-yellow-300 via-amber-400 to-yellow-300 px-3 py-1 rounded-full mb-2 shadow-md animate-shimmer">PREMIUM</span> -->

                        <div
                            class="inline-flex items-center px-5 py-3 sm:px-7 sm:py-4 rounded-full bg-black/30 backdrop-blur-lg border-3 border-amber-400/80 shadow-xl gap-3 luxury-title-box luxury-title-diamond-box">
                            <!-- <svg class="w-9 h-9 text-amber-300 drop-shadow-lg transition-transform duration-500 group-hover:scale-110"
                                fill="none" stroke="currentColor" stroke-width="2.2" viewBox="0 0 24 24">
                                <path
                                    d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z">
                                </path>
                                <path d="M5 21h14"></path>
                            </svg> -->
                            <span
                                class="text-xl sm:text-4xl font-extrabold font-serif bg-gradient-to-r from-yellow-300 via-amber-400 to-yellow-300 bg-clip-text text-transparent tracking-widest drop-shadow-[0_2px_32px_#fcd34dcc] luxury-title-diamond-glow relative group-hover:drop-shadow-[0_2px_40px_#fcd34dff] transition-all duration-500">
                                BSL Diamond
                                <span class="luxury-sparkle"></span>
                            </span>
                            <!-- <svg class="w-9 h-9 text-amber-300 drop-shadow-lg transition-transform duration-500 group-hover:scale-110"
                                fill="none" stroke="currentColor" stroke-width="2.2" viewBox="0 0 24 24">
                                <path
                                    d="M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z">
                                </path>
                                <path d="M5 21h14"></path>
                            </svg> -->
                        </div>
                        <span class="block text-lg md:text-xl font-light text-amber-200 mt-2 tracking-wide">Đặc quyền
                            không giới hạn sân bay</span>
                    </div>
                </div>
                <div class="p-4 sm:p-6 pt-0 space-y-6">
                    <div class="grid gap-3">
                        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-3 border border-amber-300/50">
                            <div class="flex justify-between items-center "><span
                                    class="font-semibold text-amber-200">Thẻ 5 lượt:</span>
                                <div class="font-bold text-base sm:text-lg text-amber-400">1,900,000


                                </div>
                            </div>
                        </div>
                        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-3 border border-amber-300/50">
                            <div class="flex justify-between items-center "><span
                                    class="font-semibold text-amber-200">Thẻ 10 lượt:</span>
                                <div class="font-bold text-base sm:text-lg text-amber-400">3,700,000


                                </div>
                            </div>
                        </div>
                        <div class="bg-black/40 backdrop-blur-sm rounded-xl p-3 border border-amber-300/50">
                            <div class="flex justify-between items-center "><span
                                    class="font-semibold text-amber-200">Thẻ 20 lượt:</span>
                                <div class="font-bold text-base sm:text-lg text-amber-400">7,200,000


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-4 sm:p-6 pt-0 space-y-4">
                    <div class="flex items-start gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-check-circle w-5 h-5 text-amber-400 flex-shrink-0 mt-1">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        <p class="text-amber-200 text-sm sm:text-base">Truy cập tất cả hệ thống phòng khách nội địa và
                            quốc
                            tế trên toàn
                            quốc</p>
                    </div>
                    <div class="flex items-start gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-check-circle w-5 h-5 text-amber-400 flex-shrink-0 mt-1">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        <p class="text-amber-200 text-sm sm:text-base">Phù hợp cho hành trình dài, công tác quốc tế</p>
                    </div>
                    <div class="flex items-start gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-check-circle w-5 h-5 text-amber-400 flex-shrink-0 mt-1">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        <p class="text-amber-200 text-sm sm:text-base">Lý tưởng cho khách cao cấp và doanh nghiệp toàn
                            cầu</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

