<div class="w-full mx-auto py-8 px-4 h-full overflow-y-scroll">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold">Chi tiết đơn đặt vé máy bay</h1>
            <div class="flex items-center mt-1"><span class="text-gray-500">Mã đơn hàng:</span>
                <div
                    class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ml-2 font-mono text-orange-600 border-orange-200 bg-orange-50">
                    {{orderAvailable?.id}}
                </div>
                <div [ngClass]="{
                    'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ml-3': true,
                    'border-blue-500 bg-blue-200 text-blue-800 hover:bg-blue-100': orderAvailable?.status === 0,
                    'border-green-500 bg-green-200 text-green-800 hover:bg-green-100': orderAvailable?.status === 1, 
                    'border-red-200 bg-red-200 text-red-800 hover:bg-red-100': orderAvailable?.status === -1,
                    'border-yellow-200 bg-yellow-200 text-yellow-800 hover:bg-yellow-100': orderAvailable?.status === 2
                }">
                    {{orderAvailable?.status === 0 ? 'Đơn mới' :
                    orderAvailable?.status === 1 ? 'Đã thanh toán' :
                    orderAvailable?.status === -1 ? 'Đóng' : 'Đang thực hiện'}}
                </div>
            </div>
        </div>
        <div class="flex gap-3 mt-4 md:mt-0">
            @if(orderAvailable?.status === 2 && state!== 403){
            <button (click)="cancelRequest()"
                class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border bg-background h-10 px-4 py-2 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-circle-x mr-2 h-4 w-4">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="m15 9-6 6"></path>
                    <path d="m9 9 6 6"></path>
                </svg>Hủy đơn hàng
            </button>
            <button (click)="onSubmitNews()"
                class="inline-flex items-center text-white justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-[#f26d43] to-[#f9a13e] hover:from-[#e05e35] hover:to-[#e8922f]"><svg
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-circle-check mr-2 h-4 w-4">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="m9 12 2 2 4-4"></path>
                </svg>Hoàn thành đơn hàng
            </button>
            }

        </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200 mb-6">
        <nav class="-mb-px flex space-x-8">
            <button
                (click)="setActiveTab('overview')"
                [class]="activeTab === 'overview' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Tổng quan
            </button>
            <button
                (click)="setActiveTab('flight-details')"
                [class]="activeTab === 'flight-details' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Chi tiết chuyến bay
            </button>
            <button
                (click)="setActiveTab('passengers')"
                [class]="activeTab === 'passengers' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Hành khách ({{getPassengerList().length}})
            </button>
            <button
                (click)="setActiveTab('notes')"
                [class]="activeTab === 'notes' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Ghi chú ({{noteUserList.length}})
            </button>
        </nav>
    </div>

    <!-- Overview Tab -->
    <div *ngIf="activeTab === 'overview'" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-1 space-y-6">
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6 bg-gray-50 border-b">
                    <h3 class="font-semibold tracking-tight text-lg flex items-center"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-user mr-2 h-5 w-5 text-gray-500">
                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>Thông tin khách hàng</h3>
                </div>
                <div class="p-6 pt-6">
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm font-medium text-gray-500">Họ và tên</div>
                            <div class="mt-1 font-medium">{{orderAvailable?.customerName}}</div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <div class="text-sm font-medium text-gray-500">Số điện thoại</div>
                                <div class="mt-1 font-medium flex items-center"><svg xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-phone mr-1 h-3 w-3 text-gray-400">
                                        <path
                                            d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                        </path>
                                    </svg>{{orderAvailable?.phoneNumber}}</div>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-500">Email</div>
                                <div class="mt-1 font-medium flex items-center"><svg xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-mail mr-1 h-3 w-3 text-gray-400">
                                        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                    </svg><span class="text-sm">{{orderAvailable?.email}}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
                <div class="flex flex-col space-y-1.5 p-6 bg-gray-50 border-b">
                    <h3 class="font-semibold tracking-tight text-lg flex items-center"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-credit-card mr-2 h-5 w-5 text-gray-500">
                            <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                            <line x1="2" x2="22" y1="10" y2="10"></line>
                        </svg>Thông tin thanh toán</h3>
                </div>
                <div class="p-6 pt-6">
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm font-medium text-gray-500">Phương thức thanh toán</div>
                            <div class="mt-1 font-medium">{{orderAvailable?.paymentMethod}}</div>
                            <!-- <div class="text-sm text-gray-500">VietcomBank</div> -->
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500">Tổng tiền</div>
                            <div class="mt-1 text-xl font-bold text-orange-600">
                                {{orderDetails?.totalPrice.toLocaleString('de-DE') + ' VND'}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="lg:col-span-2 space-y-6">
            <!-- Flight Summary -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                <div class="flex flex-col space-y-1.5 p-6 bg-gray-50 border-b">
                    <h3 class="font-semibold tracking-tight text-lg flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-plane mr-2 h-5 w-5 text-gray-500">
                            <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z"></path>
                        </svg>Tóm tắt chuyến bay
                    </h3>
                </div>
                <div class="p-6 pt-6">
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <div class="text-sm font-medium text-gray-500">Ngày khởi hành
                                    @if(orderAvailable?.returnDate){
                                    <span class="text-sm font-medium text-gray-500"> - Ngày trở về</span>
                                    }
                                </div>
                                <div class="mt-1 font-medium flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-calendar mr-1 h-4 w-4 text-gray-400">
                                        <path d="M8 2v4"></path>
                                        <path d="M16 2v4"></path>
                                        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                        <path d="M3 10h18"></path>
                                    </svg>{{formatDateTo_ddMMyyyy(orderAvailable?.departDate)}}
                                    @if(orderAvailable?.returnDate){
                                    <span class="text-sm font-medium text-gray-500"> - {{formatDateTo_ddMMyyyy(orderAvailable?.returnDate)}}</span>
                                    }
                                </div>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-500">Hành khách</div>
                                <div class="mt-1 font-medium flex flex-wrap items-center gap-3">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user mr-1 h-4 w-4 text-blue-500">
                                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                            <circle cx="12" cy="7" r="4"></circle>
                                        </svg>
                                        <span>{{orderAvailable?.adult}} Người lớn</span>
                                    </div>
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user mr-1 h-4 w-4 text-green-500">
                                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                            <circle cx="12" cy="7" r="4"></circle>
                                        </svg>
                                        <span>{{orderAvailable?.child}} Trẻ em</span>
                                    </div>
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-baby mr-1 h-4 w-4 text-purple-500">
                                            <path d="M9 12h.01"></path>
                                            <path d="M15 12h.01"></path>
                                            <path d="M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5"></path>
                                            <path d="M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1"></path>
                                        </svg>
                                        <span>{{orderAvailable?.infant}} Em bé</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Flight Details Tab -->
    <div *ngIf="activeTab === 'flight-details'" class="space-y-6">
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col space-y-1.5 p-6 bg-gray-50 border-b">
                <h3 class="font-semibold tracking-tight text-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plane mr-2 h-5 w-5 text-gray-500">
                        <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z"></path>
                    </svg>Thông tin chuyến bay
                </h3>
            </div>
            <div class="p-6 pt-6">
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div class="text-sm font-medium text-gray-500">Ngày khởi hành
                                @if(orderAvailable?.returnDate){
                                <span class="text-sm font-medium text-gray-500"> - Ngày trở về</span>
                                }
                            </div>
                            <div class="mt-1 font-medium flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-calendar mr-1 h-4 w-4 text-gray-400">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                </svg>{{formatDateTo_ddMMyyyy(orderAvailable?.departDate)}}
                                @if(orderAvailable?.returnDate){
                                <span class="text-sm font-medium text-gray-500"> - {{formatDateTo_ddMMyyyy(orderAvailable?.returnDate)}}</span>
                                }
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500">Hành khách</div>
                            <div class="mt-1 font-medium flex flex-wrap items-center gap-3">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user mr-1 h-4 w-4 text-blue-500">
                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                    <span>{{orderAvailable?.adult}} Người lớn</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user mr-1 h-4 w-4 text-green-500">
                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                    <span>{{orderAvailable?.child}} Trẻ em</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-baby mr-1 h-4 w-4 text-purple-500">
                                        <path d="M9 12h.01"></path>
                                        <path d="M15 12h.01"></path>
                                        <path d="M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5"></path>
                                        <path d="M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1"></path>
                                    </svg>
                                    <span>{{orderAvailable?.infant}} Em bé</span>
                                </div>
                            </div>
                        </div>
                    </div>
                        <div class="w-full">
                            <!-- Replace the existing PNR section -->
                            <div class="pnr-section mb-3">
                                <div class="card">
                                    <div class="card-header bg-gray-50 border-b">
                                        <h5 class="mb-0 text-gray-700 font-semibold">Mã đặt chỗ (PNR)</h5>
                                    </div>
                                    <div class="card-body p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            @if(pnrList.length > 0){
                                            @for(booking of pnrList; track booking){
                                            <div class="pnr-box p-4">
                                                <div class="flex flex-col space-y-3">
                                                    <!-- Airline Info -->
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-sm font-medium text-gray-500">Hãng hàng
                                                            không</span>
                                                        <span class="inline-flex items-center">
                                                            <img [src]="'/assets/img/airlines/' + booking.system + '.png'"
                                                                class="h-6 mr-2" *ngIf="booking.system">
                                                            <span class="font-medium">{{ booking.system }}</span>
                                                        </span>
                                                    </div>

                                                    <!-- PNR Code -->
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-sm font-medium text-gray-500">Mã PNR</span>
                                                        <span class="font-mono text-lg font-semibold"
                                                            [ngClass]="{'text-green-600': booking.status === 'Success', 'text-red-600': booking.status === 'Failed'}">
                                                            {{ booking.pnr ? formatPNR(booking.pnr) : 'Chưa có mã' }}
                                                        </span>
                                                    </div>

                                                    <!-- Status -->
                                                    <div class="flex items-center justify-between">
                                                        <span class="text-sm font-medium text-gray-500">Trạng
                                                            thái</span>
                                                        <span
                                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                            [ngClass]="{
                                                                'bg-green-100 text-green-800': booking.status === 'Success',
                                                                'bg-red-100 text-red-800': booking.status === 'Failed'
                                                              }">
                                                            {{ booking.status === 'Success' ? 'Thành công' : 'Thất bại'
                                                            }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            }
                                            }@else{
                                            <div class="col-span-full">
                                                <div
                                                    class="text-center p-6 bg-gray-50 rounded-lg border border-gray-200">
                                                    <div class="flex flex-col items-center justify-center space-y-3">
                                                        <svg class="w-12 h-12 text-gray-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                            </path>
                                                        </svg>
                                                        <h3 class="text-lg font-medium text-gray-900">Chưa có mã đặt chỗ
                                                        </h3>
                                                    </div>
                                                </div>
                                            </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class=" gap-x-4">
                            <div class="relative ">
                                @if(orderDetails?.full?.InventoriesSelected?.length > 0){
                                <div class="w-full space-y-10">
                                    @for(itinerarySelected of orderDetails.full?.InventoriesSelected; track
                                    itinerarySelected){
                                    <div class="w-full bg-gray-100 my-4 ">
                                        <div class="bg-white rounded-e-lg rounded-bl-lg ">
                                            <div class="py-[2px] flex gap-2">
                                                <button
                                                    class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-[#fb6340] to-[#fbb140] hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-800 rounded-md text-sm px-2 py-1 text-center me-2">

                                                    <span>
                                                        @if(orderDetails.full?.InventoriesSelected.length > 1){
                                                        {{$index % 2 === 1 ? 'Chiều về' : 'Chiều đi'}}
                                                        }
                                                    </span>
                                                </button>

                                                <span>
                                                    {{formatDateTo_ddMMyyyy(itinerarySelected.segment.Legs[0]?.DepartureDate)}},
                                                    Thời gian bay
                                                    {{getDurationByArray(itinerarySelected.segment.Legs)}}
                                                </span>
                                            </div>
                                            <div class="w-full">
                                                @for(leg of itinerarySelected.segment.Legs; track leg){
                                                @if($index > 0){
                                                <div
                                                    class="relative flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:border-l-[4px] before:border-dotted before:border-[#dadfe6] before:h-[30%] before:w-0 before:start-[68px] before:top-0 before:inline-block after:content-[''] after:absolute after:border-l-[4px] after:border-dotted after:border-[#dadfe6] after:h-[30%] after:w-0 after:start-[68px] after:top-[70%] after:inline-block">
                                                    <div class="flex py-4 ps-[80px] w-full">
                                                        <div class="flex flex-row items-center justify-start w-full">
                                                            <div
                                                                class="w-full text-sm py-2 px-1 bg-gray-100 rounded-lg ">
                                                                Trung chuyển tại
                                                                {{inforAirports[leg.DepartureCode]?.cityName}}
                                                                {{convertDurationToHour(itinerarySelected.segment.Legs[$index].StopTime)}}

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="absolute inline-block start-[62.5px] top-[calc(50%-8px)]">
                                                        <svg class="w-4 h-4 text-[#acb4bf] dark:text-white"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4" />
                                                        </svg>
                                                    </div>
                                                </div>
                                                }
                                                <div class="flex flex-col items-start overflow-hidden relative">
                                                    <div>
                                                        <div class="flex flex-row items-center justify-start">
                                                            <div
                                                                class="flex flex-col items-center text-[#0f294d] text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px] font-extrabold  ">
                                                                <span>
                                                                    {{ getTimeFromDateTime(leg?.DepartureDate)
                                                                    }}
                                                                </span>
                                                            </div>
                                                            <span
                                                                class="text-[#0f294d] text-[16px] font-semibold leading-[24px]">
                                                                <span>({{ leg?.DepartureCode }})</span>
                                                                {{ inforAirports[leg?.DepartureCode]?.name }}
                                                            </span>
                                                        </div>

                                                        <div
                                                            class="flex flex-1 flex-row items-center justify-start min-h-[50px] w-full  before:content-[''] before:absolute before:bg-[#dadfe6] before:rounded-[2px] before:w-[4px] before:start-[68px] before:inline-block before:h-[80%] before:top-[10%] ">
                                                            <div
                                                                class="flex flex-row-reverse flex-wrap-reverse items-center justify-end w-[97px] overflow-hidden ">
                                                                <div
                                                                    class="flex items-center justify-center bg-white  h-[24px]">
                                                                    <img [src]="'/assets/img/airlines/' + leg?.OperatingAirlines + '.png'"
                                                                        class="h-[22px] pe-7 max-w-[86px]">
                                                                </div>
                                                            </div>

                                                            <div class="flex md:py-4 py-3">

                                                                <div class="flex flex-row items-center justify-start">
                                                                    <span class="md:text-sm text-xs text-[#8592a6]">
                                                                        {{
                                                                        leg?.OperatingAirlinesName
                                                                        }}
                                                                        {{
                                                                        leg?.OperatingAirlines+
                                                                        leg?.FlightNumber }}
                                                                        {{
                                                                        itinerarySelected.inventorySelected?.BookingInfos[$index]?.FareType
                                                                        ||
                                                                        itinerarySelected.inventorySelected?.BookingInfos[$index]?.CabinName
                                                                        }}
                                                                    </span>
                                                                </div>
                                                            </div>


                                                        </div>

                                                        <div class="flex flex-row items-center justify-start">
                                                            <div
                                                                class="flex flex-col items-center text-[#0f294d] font-extrabold text-[16px]  leading-[24px] me-[43px] whitespace-nowrap w-[45px]">
                                                                <span>
                                                                    {{ getTimeFromDateTime(leg?.ArrivalDate)
                                                                    }}
                                                                </span>
                                                            </div>
                                                            <span
                                                                class="text-primary-600 text-[16px] font-semibold leading-[24px]">
                                                                <span>({{ leg?.ArrivalCode }})</span>
                                                                {{ inforAirports[leg?.ArrivalCode]?.name }}
                                                            </span>
                                                        </div>

                                                    </div>
                                                </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    }
                                </div>
                                }
                            </div>
                        </div>
                        <div data-orientation="horizontal" role="none" class="shrink-0 bg-border h-[1px] w-full"></div>
                        <div>
                            <h3 class="font-medium mb-3">Chi tiết giá vé</h3>
                            <div class="bg-gray-50 rounded-lg md:p-4 space-y-3">
                                <div [ngClass]="{'!h-auto !w-full !opacity-100 p-2': true}"
                                    class="col-span-12 border-t transition-all duration-700  ease-in-out opacity-0 w-0 h-0 overflow-hidden   grid grid-cols-1 justify-end items-center bg-[#f8e2de] rounded-b-lg border-[1px] border-white shadow-[0px_0px_15px_#cccccc96,0px_0px_15px_#44444400_inset] ">
                                    <div class="grid grid-cols-4 border-b border-gray-600 md:text-sm text-xs">
                                        <div class="text-start font-bold">
                                            Hành khách
                                        </div>
                                        <div class="text-end font-bold">
                                            Giá vé
                                        </div>
                                        <div class="text-end font-bold">
                                            Thuế
                                        </div>
                                        <div class="text-end font-bold">
                                            Giá Bán
                                        </div>
                                    </div>
                                    @for(fareInfo of pricePaxInfor; track
                                    fareInfo){
                                    <div class="grid grid-cols-4 border-b border-gray-600 py-1 md:text-sm text-xs">
                                        <div class="text-start">
                                            {{
                                            getPassengerDescription(fareInfo?.PaxType)
                                            }}
                                        </div>
                                        <div class="text-end">
                                            {{fareInfo.Fare | numberFormat}}

                                        </div>
                                        <div class="text-end">
                                            {{fareInfo.Tax | numberFormat}}

                                        </div>
                                        <div class="text-end">
                                            {{(fareInfo.Fare + fareInfo.Tax) | numberFormat}}
                                        </div>
                                    </div>
                                    }
                                    <div class=" text-right md:text-sm text-xs">
                                        Phí dịch vụ: <strong class="md:text-xl text-base font-bold text-primary-600">
                                            <strong class="text-base text-primary-600">
                                                {{
                                                servicePrice | numberFormat
                                                }} </strong>
                                        </strong>VNĐ
                                    </div>
                                    <div *ngIf="orderDetails?.transactionFee" class=" text-right md:text-sm text-xs">
                                        Phí giao dịch: <strong class="md:text-xl text-base font-bold text-primary-600">
                                            <strong class="text-base text-primary-600">
                                                {{orderDetails?.transactionFee.toLocaleString('de-DE') + ' VND'}}
                                                 </strong>
                                        </strong>VNĐ
                                    </div>
                                    <div *ngIf="orderDetails?.voucherDiscount" class=" text-right md:text-sm text-xs">
                                        Voucher: <strong class="md:text-xl text-base font-bold text-primary-600">
                                            <strong class="text-base text-green-600">
                                               {{orderDetails?.voucherDiscount.toLocaleString('de-DE') + ' VND'}}
                                                 </strong>
                                        </strong>VNĐ
                                    </div>

                                </div>
                                <div data-orientation="horizontal" role="none"
                                    class="shrink-0 bg-border h-[1px] w-full"></div>
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">Tổng cộng</div>
                                    <div class="text-lg font-bold text-orange-600">
                                        {{orderDetails?.totalPrice.toLocaleString('de-DE') + ' VND'}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Passengers Tab -->
    <div *ngIf="activeTab === 'passengers'" class="space-y-6">
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col space-y-1.5 p-6 bg-gray-50 border-b">
                <h3 class="font-semibold tracking-tight text-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-users mr-2 h-5 w-5 text-gray-500">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>Danh sách hành khách
                </h3>
            </div>
            <div class="p-6 pt-6 px-0">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-xs text-gray-500 border-b">
                                <th class="font-medium text-left pl-6 pr-2 py-2">HỌ VÀ TÊN</th>
                                <th class="font-medium text-left px-2 py-2">NGÀY SINH</th>
                                <th class="font-medium text-left px-2 py-2">GIỚI TÍNH</th>
                                <th class="font-medium text-left px-2 py-2">LOẠI</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(pax of orderDetails?.paxList; track pax){
                            <tr class="border-b">
                                <td class="pl-6 pr-2 py-3">
                                    <div>
                                        {{pax?.fullname}}
                                    </div>
                                    <div *ngIf="pax?.withInfant">
                                        <span class="text-xs text-red-500 ">
                                            * Em bé: {{pax?.withInfant?.fullname}} -
                                            {{formatDateTo_ddMMyyyy(pax?.withInfant?.birthday)}} -
                                            @switch (pax?.withInfant?.gender) {
                                            @case ('MSTR') {
                                            Bé trai
                                            }
                                            @case ('MISS') {
                                            Bé gái
                                            }
                                            @default {
                                            Khác
                                            }
                                            }
                                        </span>
                                    </div>
                                    <div>
                                        @for(baggage of pax.baggages; track baggage){
                                        <div class="text-xs text-gray-500 dark:text-gray-400" *ngIf="baggage?.SsrCode">
                                            {{baggage?.type}} - {{baggage?.WeightBag}} KG
                                        </div>
                                        }
                                    </div>
                                </td>
                                <td class="px-2 py-3">
                                    {{formatDateTo_ddMMyyyy(pax?.birthday)}}
                                </td>
                                <td class="px-2 py-3">
                                    @switch (pax.gender) {
                                    @case ('MR') {
                                    Nam
                                    }
                                    @case ('MRS') {
                                    Nữ
                                    }
                                    @case ('MS') {
                                    Nữ
                                    }
                                    @default {
                                    Khác
                                    }
                                    }
                                </td>
                                <td class="px-2 py-3">
                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 {{pax?.type === 'ADT' ? 'bg-blue-50 text-blue-700 border-blue-200' : pax?.type === 'CNN' ? 'bg-green-50 text-green-700 border-green-200' : 'bg-purple-50 text-purple-700 border-purple-200'}}">
                                        @switch (pax.type) {
                                        @case ('adult') {
                                        Người lớn
                                        }
                                        @case ('child') {
                                        Trẻ em
                                        }
                                        @case ('infant') {
                                        Em bé
                                        }
                                        @default {
                                        Khác
                                        }
                                        }
                                    </div>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Tab -->
    <div *ngIf="activeTab === 'notes'" class="space-y-6">
        <div class="rounded-lg border-2 border-blue-400 bg-blue-50 text-card-foreground shadow-md">
            <div class="flex flex-col space-y-1.5  px-6 pt-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="lucide lucide-users mr-2 h-5 w-5 text-blue-500">
                            <path d="M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z"></path>
                            <path d="M15 3v4a2 2 0 0 0 2 2h4"></path>
                        </svg>
                        <span class="text-2xl font-semibold leading-none tracking-tight">Ghi chú</span>
                        <span *ngIf="noteUserList.length > 0"
                            class="ml-2 bg-blue-100 text-blue-700 rounded-full px-2 py-0.5 text-xs font-bold">
                            {{ noteUserList.length }}
                        </span>
                    </div>
                    <button (click)="openModalNote(true)"
                        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  text-white h-8 rounded-md px-3 bg-blue-500 hover:bg-blue-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="text-white w-4 h-4 mr-1">
                            <path d="M12 20h9"></path>
                            <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"></path>
                        </svg>Thêm ghi chú
                    </button>
                </div>
            </div>
            <div class="px-6 pb-4 pt-0 space-y-4">
                <div class="space-y-3">
                    <!-- Danh sách ghi chú ngoài block: chỉ hiển thị ghi chú mới nhất -->
                    <div *ngIf="noteUserList.length > 0">
                        <ng-container *ngIf="!showAllNotes; else allNotesBlock">
                            <div class="flex items-center justify-between pt-4">
                                <div class="flex-1 truncate text-sm text-gray-700">
                                    <span class="truncate block max-w-[80vw]">{{
                                        noteUserList[noteUserList.length-1]?.content }}</span>
                                    <span class="ml-2 text-xs text-gray-400 hidden group-hover:inline">{{
                                        noteUserList[noteUserList.length-1]?.time | date:'HH:mm dd/MM/yyyy'
                                        }}</span>
                                </div>
                                <button (click)="showAllNotes = true"
                                    class="ml-3 text-blue-600 hover:underline text-xs font-medium whitespace-nowrap">Xem
                                    tất cả</button>
                            </div>
                        </ng-container>
                        <ng-template #allNotesBlock>
                            <div class="pt-4">
                                <div *ngFor=" let note of noteUserList; let i=index"
                                    class="border rounded-lg p-2 mb-2 bg-gray-50">
                                    <div *ngIf="editNoteIndex === i; else viewNote">
                                        <textarea [(ngModel)]="editNoteContent"
                                            class="w-full rounded border p-1 text-sm" maxlength="500"
                                            rows="2"></textarea>
                                        <div class="flex gap-2 mt-1 items-center">
                                            <button (click)="saveEditNoteUser(i)"
                                                [disabled]="!editNoteContent || getTotalNoteContentLength(true, editNoteContent, i) > 500"
                                                class="px-3 py-1 rounded bg-blue-500 text-white text-xs hover:bg-blue-600">Lưu</button>
                                            <button (click)="cancelEditNoteUser()"
                                                class="px-3 py-1 rounded bg-gray-200 text-xs hover:bg-gray-300">Hủy</button>
                                            <span class="text-xs text-gray-400 ml-2 mt-1">{{
                                                getTotalNoteContentLength(true, editNoteContent, i) }}/500 ký
                                                tự</span>
                                        </div>
                                    </div>
                                    <ng-template #viewNote>
                                        <div class="text-gray-800">{{ note.content }}</div>
                                    </ng-template>
                                    <div class="flex items-center justify-between">
                                        <div class="text-xs text-gray-500 mt-1">{{ note.time | date:'HH:mm
                                            dd/MM/yyyy' }}</div>
                                        <div class="flex gap-1">
                                            <button (click)="startEditNoteUser(i)"
                                                class="group ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent hover:text-accent-foreground  rounded-md px-3 ">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="group-hover:text-blue-500 w-3 h-3">
                                                    <path d="M12 20h9"></path>
                                                    <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z"></path>
                                                </svg>
                                            </button>
                                            <button (click)="deleteNoteUser(i)"
                                                class="group ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  hover:bg-accent rounded-md px-3 text-red-600 hover:text-red-700">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="group-hover:bg-red-500 group-hover:rounded group-hover:text-white w-3 h-3">
                                                    <path d="M3 6h18"></path>
                                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                    <line x1="10" x2="10" y1="11" y2="17"></line>
                                                    <line x1="14" x2="14" y1="11" y2="17"></line>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button (click)="showAllNotes = false"
                                    class="text-blue-600 hover:underline text-sm font-medium whitespace-nowrap">Thu
                                    gọn</button>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>

        <!-- History Section -->
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm" data-v0-t="card">
            <!-- Tab Header -->
            <div class="border-b bg-gray-50">
                <nav class="flex">
                    <button
                        (click)="showHistoryTab = !showHistoryTab"
                        class="flex items-center px-6 py-4 text-sm font-medium transition-all duration-200 hover:bg-white hover:shadow-sm rounded-t-lg tab-button"
                        [class.active]="showHistoryTab"
                        [class]="'border-b-2 ' + (showHistoryTab ? 'border-blue-500 text-blue-600 bg-white' : 'border-transparent text-gray-600 hover:text-gray-800')">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="mr-2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12,6 12,12 16,14"></polyline>
                        </svg>
                        Lịch sử hoạt động
                        @if (historyList.length > 0) {
                            <span class="ml-2 inline-flex items-center justify-center px-2 py-0.5 text-xs font-semibold rounded-full"
                                  [class]="showHistoryTab ? 'bg-blue-100 text-blue-700' : 'bg-gray-200 text-gray-700'">
                                {{historyList.length}}
                            </span>
                        }
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="ml-3 transition-transform duration-200" [class.rotate-180]="showHistoryTab">
                            <path d="m6 9 6 6 6-6"></path>
                        </svg>
                    </button>
                </nav>
            </div>
            
            <!-- Tab Content -->
            <div class="overflow-hidden transition-all duration-300 ease-in-out" 
                 [style.max-height]="showHistoryTab ? '1000px' : '0px'">
                <div class="tab-content" 
                     [class.opacity-0]="!showHistoryTab" 
                     [class.opacity-100]="showHistoryTab" 
                     [class.translate-y-2]="!showHistoryTab"
                     [class.translate-y-0]="showHistoryTab"
                     style="transition: opacity 0.3s ease, transform 0.3s ease;">
                    <div class="p-6">
                        @if (historyList && historyList.length > 0) {
                            <div class="space-y-4 history-timeline">
                                @for (history of (showAllHistory ? historyList : historyList.slice(0, 5)); track history.id) {
                                    <div class="flex items-start space-x-3 p-4 rounded-lg border border-gray-100 hover:border-gray-200 history-item">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm history-action-icon"
                                                 [ngClass]="getActionColor(history.action)">
                                                {{getActionIcon(history.action)}}
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between">
                                                <p class="text-sm font-medium text-gray-900">
                                                    {{getActionText(history.action)}}
                                                </p>
                                                <time class="text-xs text-gray-500 whitespace-nowrap ml-2">
                                                    {{history.createdDate}}
                                                </time>
                                            </div>
                                            @if (history.oldStatus !== history.newStatus) {
                                                <div class="mt-2 flex items-center space-x-2 text-xs">
                                                    <span class="status-badge old-status">
                                                        {{history.oldStatusName}}
                                                    </span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" 
                                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                                         stroke-linejoin="round" class="text-gray-400">
                                                        <path d="M5 12h14"></path>
                                                        <path d="m12 5 7 7-7 7"></path>
                                                    </svg>
                                                    <span class="status-badge new-status">
                                                        {{history.newStatusName}}
                                                    </span>
                                                </div>
                                            }
                                            @if (history.note) {
                                                <p class="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded border-l-4 border-blue-200">
                                                    {{history.note}}
                                                </p>
                                            }
                                            <div class="mt-3 flex items-center space-x-4 text-xs text-gray-500">
                                                <span class="flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" 
                                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                                         stroke-linejoin="round" class="mr-1">
                                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                                        <circle cx="12" cy="7" r="4"></circle>
                                                    </svg>
                                                    {{history.userName}}
                                                </span>
                                                <span class="flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" 
                                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                                         stroke-linejoin="round" class="mr-1">
                                                        <circle cx="12" cy="12" r="3"></circle>
                                                        <path d="M12 1v6m0 6v6"></path>
                                                        <path d="m3 12 6 0m6 0 6 0"></path>
                                                    </svg>
                                                    {{history.ipAddress}}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                }
                                
                                @if (historyList.length > 5) {
                                    <div class="text-center pt-4 border-t">
                                        @if (!showAllHistory) {
                                            <button (click)="showAllHistory = true"
                                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" 
                                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                                     stroke-linejoin="round" class="mr-2">
                                                    <path d="m6 9 6 6 6-6"></path>
                                                </svg>
                                                Xem tất cả {{historyList.length}} hoạt động
                                            </button>
                                        } @else {
                                            <button (click)="showAllHistory = false"
                                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" 
                                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" 
                                                     stroke-linejoin="round" class="mr-2">
                                                    <path d="m18 15-6-6-6 6"></path>
                                                </svg>
                                                Thu gọn
                                            </button>
                                        }
                                    </div>
                                }
                            </div>
                        } @else {
                            <div class="text-center py-12">
                                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" 
                                     fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" 
                                     stroke-linejoin="round" class="mx-auto text-gray-300 mb-4">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <h3 class="text-sm font-medium text-gray-900 mb-1">Chưa có hoạt động</h3>
                                <p class="text-sm text-gray-500">Lịch sử hoạt động sẽ được hiển thị tại đây</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Modal Notification -->
<ng-template #modalNotification let-modal>
    <div class="relative w-full h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-bold text-primary-600 dark:text-white">
                    {{titleModal}}
                </h3>

            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5 py-4 overflow-y-auto">
                <div class="max-h-[60vh] h-full max-w-lg">
                    <!-- content notification -->
                    {{contentModal}}
                </div>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center justify-end pb-4 px-4 rounded-b dark:border-gray-600">
                <button (click)="goBack()"
                    class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-[#fb6340] to-[#fbb140] hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                    Quay lại
                </button>
            </div>
        </div>
    </div>
</ng-template>

<!-- Modal Add Note -->
<ng-template #modalNote let-modal>
    <div class="relative w-full h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-bold text-primary-600 dark:text-white">
                    Ghi chú đơn hàng
                </h3>
            </div>
            <!-- Modal body -->
            <div class="p-4 md:p-5 py-4 overflow-y-auto">
                <div class="max-h-[60vh] h-full max-w-lg">
                    <div class="space-y-3">

                        <!-- Form thêm ghi chú -->
                        <div>
                            <label
                                class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                for="note-content">Nội dung ghi chú</label>
                            <textarea
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                id="note-content" [(ngModel)]="newNoteContent" maxlength="500"
                                placeholder="Nhập nội dung ghi chú..." rows="3">
                            </textarea>
                            <div class="text-xs text-gray-400 text-right mt-1">
                                {{ getTotalNoteContentLength(true, newNoteContent, null) }}/500 ký tự
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="flex items-center justify-end pb-4 px-4 rounded-b dark:border-gray-600">
                <button (click)="addNoteUser()" [disabled]="!newNoteContent || (newNoteContent.length || 0) > 500"
                    class="h-auto w-auto border border-white group flex tracking-tight gap-2 font-bold items-center justify-center text-white bg-gradient-to-br from-[#fb6340] to-[#fbb140] hover:bg-gradient-to-bl focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-800 rounded-lg text-sm px-6 py-3 text-center me-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-save w-4 h-4 mr-1">
                        <path
                            d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z">
                        </path>
                        <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path>
                        <path d="M7 3v4a1 1 0 0 0 1 1h7"></path>
                    </svg>
                    Lưu ghi chú
                </button>
                <button (click)="openModalNote(false)"
                    class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  border-input bg-background hover:bg-accent hover:text-accent-foreground border h-9 rounded-md px-3">
                    Hủy
                </button>
            </div>
        </div>
    </div>
</ng-template>