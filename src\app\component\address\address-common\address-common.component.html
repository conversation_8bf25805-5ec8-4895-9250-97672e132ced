<div #addressSection class="pt-10" [attr.data-aos]="_animation">
    <div class="px-[10%] max-md:px-2">
        <div class="w-full bg-transparent container-common rounded-lg p-4">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">{{_title}}</h1>
            
            <!-- Loading skeleton when loading -->
            <ng-container *ngIf="isLoading">
                <app-loading-skeleton></app-loading-skeleton>
            </ng-container>

            <!-- Content when loaded -->
            <ng-container *ngIf="isVisible && !isLoading">
                    @if(_showAdvantage){
                    <ul class="flex flex-row gap-3 mt-2 w-full overflow-x-auto">
                        <li><a class="flex flex-row items-center gap-1 whitespace-nowrap dark:text-gray-200">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                    class="text-primary-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12.01 6.001C6.5 1 1 8 5.782 13.001L12.011 20l6.23-7C23 8 17.5 1 12.01 6.002Z" />
                                </svg>
                                <span>
                                    {{translatePage?.['S0004-0025_0002']}}
                                </span></a></li>
                        <li><a class="flex flex-row items-center gap-1 whitespace-nowrap dark:text-gray-200">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                    class="text-primary-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-width="2"
                                        d="M11.083 5.104c.35-.8 1.485-.8 1.834 0l1.752 4.022a1 1 0 0 0 .84.597l4.463.342c.9.069 1.255 1.2.556 1.771l-3.33 2.723a1 1 0 0 0-.337 1.016l1.03 4.119c.214.858-.71 1.552-1.474 1.106l-3.913-2.281a1 1 0 0 0-1.008 0L7.583 20.8c-.764.446-1.688-.248-1.474-1.106l1.03-4.119A1 1 0 0 0 6.8 14.56l-3.33-2.723c-.698-.571-.342-1.702.557-1.771l4.462-.342a1 1 0 0 0 .84-.597l1.753-4.022Z" />
                                </svg>
                                <span>
                                    {{translatePage?.['S0004-0025_0003']}}
                                </span></a></li>
                        <li><a class="flex flex-row items-center gap-1 whitespace-nowrap dark:text-gray-200">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                    class="text-primary-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2"
                                        d="m8.032 12 1.984 1.984 4.96-4.96m4.55 5.272.893-.893a1.984 1.984 0 0 0 0-2.806l-.893-.893a1.984 1.984 0 0 1-.581-1.403V7.04a1.984 1.984 0 0 0-1.984-1.984h-1.262a1.983 1.983 0 0 1-1.403-.581l-.893-.893a1.984 1.984 0 0 0-2.806 0l-.893.893a1.984 1.984 0 0 1-1.403.581H7.04A1.984 1.984 0 0 0 5.055 7.04v1.262c0 .527-.209 1.031-.581 1.403l-.893.893a1.984 1.984 0 0 0 0 2.806l.893.893c.372.372.581.876.581 1.403v1.262a1.984 1.984 0 0 0 1.984 1.984h1.262c.527 0 1.031.209 1.403.581l.893.893a1.984 1.984 0 0 0 2.806 0l.893-.893a1.985 1.985 0 0 1 1.403-.581h1.262a1.984 1.984 0 0 0 1.984-1.984V15.7c0-.527.209-1.031.581-1.403Z" />
                                </svg>
                                <span>
                                    {{translatePage?.['S0004-0025_0004']}}
                                </span></a></li>
                    </ul>
                    }

                    <!-- list category -->
                    @if(_showCategory){
                    <ul class="w-full flex flex-row overflow-x-auto gap-2 mt-2">
                        <li routerLink="/address"
                            class="active border dark:text-gray-200 border-primary-600 rounded-md px-2 py-1 hover:bg-primary-600 hover:text-white cursor-pointer  whitespace-nowrap">
                            {{translatePage?.['S0004-0025_0005']}}
                        </li>
                        @for(item of provinces; track item)
                        {
                        <li routerLink="/address" [queryParams]="{ keyword: item }"
                            class="border border-primary-600  dark:text-gray-200 rounded-md px-2 py-1 hover:bg-primary-600 hover:text-white cursor-pointer  whitespace-nowrap">
                            {{item}}</li>
                        }
                    </ul>
                    }
                    
                    <!-- Address carousel -->
                    @if(dataAddress && dataAddress.items && dataAddress.items.length > 0) {
                        <ngx-slick-carousel [config]="slideConfig" class="carousel mt-4">
                            @for (item of dataAddress.items; track item.id)
                            {
                            <a ngxSlickItem
                                [href]="item.hrefLinkDefault ? item.hrefLinkDefault : '/address/' + item.seoTitle"
                                class="slide max-w-sm bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cursor-pointer md:mx-2 mx-1">
                                <div class="rounded-t-lg overflow-hidden w-full relative">
                                    <img class="rounded-t-lg hover:scale-110 w-full h-60 max-sm:h-40 ease-in-out delay-300 bg-cover object-cover transition-all duration-500"
                                        src="{{item.image}}" loading="lazy" alt="{{item.name}}" />
                                </div>
                                <div class="px-4 py-2 max-sm:px-2">
                                    <a>
                                        <h5
                                            class="max-sm:h-10 text-base max-sm:text-sm font-bold tracking-tight text-gray-900 dark:text-white line-clamp-1">
                                            {{item.name}}</h5>
                                    </a>
                                    <div class="mb-3 max-sm:mb-1 font-normal text-gray-700 dark:text-gray-400">
                                        <span class="text-sm max-sm:text-xs">{{item.province}} - {{item.country}}</span>
                                    </div>
                                    <div class="flex items-center justify-end">
                                        <a
                                            class="bg-primary-600 max-md:hidden inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white cursor-pointer rounded-lg hover:bg-none hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                            <span class="line-clamp-1">
                                                {{translatePage?.['S0004-0025_0006']}}
                                            </span>
                                            <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="M1 5h12m0 0L9 1m4 4L9 9" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </a>
                            }
                        </ngx-slick-carousel>
                    } @else {
                        <!-- No data message -->
                        <div class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">Không có dữ liệu để hiển thị</p>
                        </div>
                    }
                </ng-container>

                <!-- Fallback skeleton when not visible yet -->
                <ng-container *ngIf="!isVisible">
                    <app-loading-skeleton></app-loading-skeleton>
                </ng-container>

        </div>
    </div>
</div>