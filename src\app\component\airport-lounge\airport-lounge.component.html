<main>
    <section class="relative  flex items-center overflow-hidden ">
        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }"
            class="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br  opacity-20 rounded-full blur-2xl animate-float">
        </div>
        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }"
            class="absolute bottom-32 right-32 w-48 h-48 bg-gradient-to-br  opacity-15 rounded-full blur-3xl animate-float"
            style="animation-delay: 2s;">
        </div>
        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }"
            class="absolute top-1/2 right-20 w-24 h-24 bg-gradient-to-br  opacity-25 rounded-full blur-xl animate-float"
            style="animation-delay: 1s;">
        </div>
        <div class="max-w-6xl mx-auto relative z-10 px-4 pt-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="space-y-8">
                    <div class="relative inline-block">
                        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }" class="absolute inset-0 bg-gradient-to-r rounded-full blur-lg animate-pulse">

                        </div>
                        <div [ngClass]="{' text-emerald-600': selectedRegion === 'north',
                            ' text-orange-600': selectedRegion === 'central',
                            ' text-purple-600': selectedRegion === 'south'
                        }"
                            class="relative px-6 py-3 rounded-full bg-white/90 backdrop-blur-sm shadow-xl flex items-center gap-3 border border-white/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-plane h-5 w-5  animate-bounce">
                                <path
                                    d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                </path>
                            </svg>
                            <span class="font-bold ">Phòng Chờ Sân Bay Cao Cấp</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="lucide lucide-sparkles h-4 w-4  animate-spin"
                                style="animation-duration: 3s;">
                                <path
                                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z">
                                </path>
                                <path d="M20 3v4"></path>
                                <path d="M22 5h-4"></path>
                                <path d="M4 17v2"></path>
                                <path d="M5 18H3"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                            <span [ngClass]="{' from-emerald-400 to-teal-500 ': selectedRegion === 'north',
                                ' from-orange-400 to-red-400 ': selectedRegion === 'central',
                                ' from-purple-400 to-fuchsia-400 ': selectedRegion === 'south'
                             }" class="bg-gradient-to-r  bg-clip-text text-transparent">Phòng
                                Chờ</span>
                            <br>
                            <span class="text-gray-800">Toàn Việt Nam</span>
                        </h1>
                        <p class="text-xl text-gray-700 leading-relaxed max-w-2xl">
                            Tạm biệt sự hối hả thường ngày, hãy để từng khoảnh khắc chờ bay trở thành khoảng thời gian
                            thư giãn đích thực.
                        </p>
                        <p class="text-xl text-gray-700 leading-relaxed max-w-2xl">
                            Đẳng cấp không chỉ đến từ nơi bạn đến, mà còn từ cách bạn bắt đầu,
                            Ngọc Mai Travel mở bán dịch vụ phòng chờ với mức giá cực kỳ hấp dẫn.
                        </p>
                    </div>
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-800">Chọn khu vực:</h3>
                        <div class="flex flex-wrap gap-3">
                            <button (click)="setSelectedRegion('north')"
                                [ngClass]="{'bg-gradient-to-r from-emerald-400 to-teal-500 text-white border-transparent shadow-lg scale-105': selectedRegion === 'north', 'bg-white/80 text-emerald-400 border-emerald-400 hover:bg-white' : selectedRegion !== 'north'}"
                                class="px-6 py-3 rounded-xl transition-all duration-300 border-2   hover:shadow-md">
                                <div class="text-center">
                                    <div class="font-bold">Miền Bắc</div>
                                    <div class="text-xs opacity-90">{{regions.north.items.length}} Phòng chờ</div>
                                </div>
                            </button>
                            <button (click)="setSelectedRegion('central')"
                                [ngClass]="{'bg-gradient-to-r from-orange-400 to-red-400 text-white border-transparent shadow-lg scale-105': selectedRegion === 'central', 'bg-white/80 text-orange-400 border-orange-400 hover:bg-white': selectedRegion !== 'central'}"
                                class="px-6 py-3 rounded-xl transition-all duration-300 border-2   hover:shadow-md">
                                <div class="text-center">
                                    <div class="font-bold">Miền Trung</div>
                                    <div class="text-xs opacity-90">{{regions.central.items.length}} Phòng chờ</div>
                                </div>
                            </button>
                            <button (click)="setSelectedRegion('south')"
                                [ngClass]="{'bg-gradient-to-r from-purple-400 to-fuchsia-400 text-white border-transparent shadow-lg scale-105': selectedRegion === 'south', 'bg-white/80 text-purple-400 border-purple-400 hover:bg-white': selectedRegion !== 'south'}"
                                class="px-6 py-3 rounded-xl transition-all duration-300 border-2  hover:shadow-md">
                                <div class="text-center">
                                    <div class="font-bold">Miền Nam</div>
                                    <div class="text-xs opacity-90">{{regions.south.items.length}} Phòng chờ</div>
                                </div>
                            </button>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4 pb-8 items-center">
                        <button (click)="scrollToRegion(selectedRegion)" [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary hover:bg-primary/90 h-10 group bg-gradient-to-r  hover:shadow-xl text-white px-4 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            Khám
                            phá ngay
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="lucide lucide-arrow-right ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7">

                                </path>
                            </svg>
                        </button>
                        <a href="tel:02822293030"
                            class="book-button-explosive group relative inline-flex items-center justify-center gap-3 whitespace-nowrap rounded-2xl h-12 px-6 py-5 text-xl font-bold shadow-2xl focus:outline-none focus:ring-4 focus:ring-orange-300/60 active:scale-95 overflow-hidden transform transition-all duration-300">
                            
                            <!-- Nền gradient động với hiệu ứng sóng -->
                            <div class="background-waves absolute inset-0"></div>
                            
                            <!-- Shimmer effect mạnh hơn -->
                            <!-- <div class="shimmer-effect-explosive absolute inset-0"></div> -->
                            
                            <!-- Hiệu ứng tia sáng bùng nổ -->
                            <div class="light-rays absolute inset-0 pointer-events-none">
                                <div class="ray ray-1"></div>
                                <div class="ray ray-2"></div>
                                <div class="ray ray-3"></div>
                                <div class="ray ray-4"></div>
                                <div class="ray ray-5"></div>
                                <div class="ray ray-6"></div>
                            </div>
                            
                            
                            
                            <!-- Hiệu ứng particle bùng nổ -->
                            <div class="particles-explosion absolute inset-0 pointer-events-none z-15">
                                <div class="particle particle-1"></div>
                                <div class="particle particle-2"></div>
                                <div class="particle particle-3"></div>
                                <div class="particle particle-4"></div>
                                <div class="particle particle-5"></div>
                                <div class="particle particle-6"></div>
                                <div class="particle particle-7"></div>
                                <div class="particle particle-8"></div>
                            </div>

                            <!-- Phone icon with explosive animation -->
                            <svg class="relative z-40 w-6 h-6 drop-shadow-xl icon-explosive"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M2 3.5A1.5 1.5 0 013.5 2h1.148a1.5 1.5 0 011.465 1.175l.716 3.223a1.5 1.5 0 01-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 006.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 011.767-1.052l3.223.716A1.5 1.5 0 0118 15.352V16.5a1.5 1.5 0 01-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 012.43 8.326 13.019 13.019 0 012 5V3.5z"
                                    clip-rule="evenodd" />
                            </svg>

                            <!-- Text với hiệu ứng nổi bật -->
                            <span class="relative z-40 font-black tracking-widest text-explosive text-xl">
                                BOOK NGAY
                            </span>
                        </a>
                    </div>
                </div>
                <div class="relative">
                    <div class="relative group">
                        <div [ngClass]="{' from-emerald-400 to-teal-500 ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }"
                            class="absolute inset-0 bg-gradient-to-r  rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 animate-pulse">
                        </div>
                        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }"
                            class="relative bg-gradient-to-r  rounded-3xl p-1 transform group-hover:scale-105 transition-transform duration-500">
                            <div class="bg-white rounded-3xl overflow-hidden">
                                <img [alt]="regions[selectedRegion]?.describe" loading="lazy" width="700" height="600"
                                    decoding="async" data-nimg="1"
                                    class="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-110"
                                    [src]="regions[selectedRegion]?.img" style="color: transparent;">
                            </div>
                        </div>
                        <div
                            class="absolute -top-6 -left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/30 transform hover:scale-110 transition-transform duration-300">
                            <div class="flex items-center gap-3">
                                <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }" class="w-12 h-12 bg-gradient-to-br  rounded-xl flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-map-pin h-6 w-6 text-white">
                                        <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                        <circle cx="12" cy="10" r="3"></circle>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-bold text-gray-800">{{regions[selectedRegion]?.name}}</div>
                                    <div class="text-sm text-gray-600">{{regions[selectedRegion]?.airportCount}} sân bay
                                    </div>
                                    <!-- trung 8 nam 8 -->
                                </div>
                            </div>
                        </div>
                        <div
                            class="absolute -bottom-6 -right-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/30 transform hover:scale-110 transition-transform duration-300">
                            <div class="text-center">
                                <div [ngClass]="{'text-emerald-600': selectedRegion === 'north',
                            'text-orange-600': selectedRegion === 'central',
                            'text-purple-600': selectedRegion === 'south'
                                }" class="text-2xl font-bold ">24/7</div>
                                <div class="text-sm text-gray-600">Hỗ trợ</div>
                            </div>
                        </div>
                        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }" class="absolute top-8 right-8 w-4 h-4 bg-gradient-to-br rounded-full animate-bounce">
                        </div>
                        <div [ngClass]="{' from-emerald-400 to-teal-500  ': selectedRegion === 'north',
                            ' from-orange-400 to-red-400  ': selectedRegion === 'central',
                            ' from-purple-400 to-fuchsia-400  ': selectedRegion === 'south'
                        }" class="absolute top-16 right-16 w-2 h-2 bg-gradient-to-br rounded-full animate-bounce"
                            style="animation-delay: 0.5s;">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="py-20 relative overflow-hidden max-md:px-2">
        <div class="absolute inset-0 opacity-40">

            <div class="absolute inset-0 bg-gradient-to-b from-transparent via-white/30 to-transparent animate-pulse"
                style="animation-delay: 1.5s;"></div>
        </div>
        <div
            class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-400 via-violet-400 to-fuchsia-400 opacity-10 rounded-full blur-3xl animate-float">
        </div>
        <div class="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-400 via-violet-400 to-fuchsia-400 opacity-15 rounded-full blur-2xl animate-float"
            style="animation-delay: 2s;">
        </div>
        <div class="max-w-6xl mx-auto relative z-10 ">
            <div class="text-center mb-16">
                <div class="relative inline-block mb-8">
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 rounded-full blur-md animate-pulse">
                    </div>
                    <div
                        class="relative px-8 py-4 rounded-full bg-white/90 backdrop-blur-sm shadow-2xl flex items-center gap-3 border border-white/30">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-sparkles h-6 w-6 text-purple-600 animate-spin"
                            style="animation-duration: 3s;">
                            <path
                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z">
                            </path>
                            <path d="M20 3v4"></path>
                            <path d="M22 5h-4"></path>
                            <path d="M4 17v2"></path>
                            <path d="M5 18H3"></path>
                        </svg>
                        <span class="font-bold text-lg text-purple-600">Ưu Điểm Nổi Bật</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-sparkles h-6 w-6 text-purple-600 animate-spin"
                            style="animation-duration: 3s; animation-delay: 1s;">
                            <path
                                d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z">
                            </path>
                            <path d="M20 3v4"></path>
                            <path d="M22 5h-4"></path>
                            <path d="M4 17v2"></path>
                            <path d="M5 18H3"></path>
                        </svg>
                    </div>
                </div>
                <h2 class="text-5xl md:text-6xl font-bold mb-6">
                    <span
                        class="bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 bg-clip-text text-transparent text-4xl md:text-5xl lg:text-6xl">Trải
                        Nghiệm</span><br><span class="text-gray-800 text-3xl md:text-4xl lg:text-5xl">Đẳng Cấp Quốc
                        Tế</span>
                </h2>
                <p class="md:text-xl text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">Khám phá những tiện nghi
                    cao cấp và
                    dịch vụ chuyên nghiệp tại các phòng chờ sân bay hàng đầu Việt Nam</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
                <div class="relative group cursor-pointer transition-all duration-500 ">
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-3xl blur-sm group-hover:blur-md transition-all duration-500 ">
                    </div>
                    <div
                        class="relative bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-3xl p-[2px] transform transition-all duration-500">
                        <div class="bg-white/95 backdrop-blur-sm rounded-3xl p-8 h-full">
                            <div class="flex justify-between items-start  md:mb-6 mb-2">
                                <div
                                    class="w-16 h-16 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-400 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-6">
                                    <div class="text-white">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-wifi h-8 w-8">
                                            <path d="M12 20h.01"></path>
                                            <path d="M2 8.82a15 15 0 0 1 20 0"></path>
                                            <path d="M5 12.859a10 10 0 0 1 14 0"></path>
                                            <path d="M8.5 16.429a5 5 0 0 1 7 0"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div
                                    class="px-3 py-1 rounded-full text-emerald-600 bg-white border-2 border-current text-xs font-bold">
                                    Miễn phí</div>
                            </div>

                            <h3 class="md:text-2xl text-xl font-bold mb-4 text-gray-800">
                                Không gian yên tĩnh & riêng tư
                            </h3>
                            <p class="text-gray-600 leading-relaxed mb-6">Được thiết kế để tách biệt khỏi sự ồn ào, đông
                                đúc của nhà ga chung. Ghế ngồi rộng rãi, có khu vực làm việc riêng, ghế nằm thư giãn,
                                ánh sáng dịu và không gian sạch sẽ.</p>

                        </div>
                    </div>
                    <div
                        class="absolute top-4 right-4 w-3 h-3 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-400 rounded-full animate-ping hidden group-hover:block">
                    </div>
                    <div class="absolute top-8 right-8 w-2 h-2 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-400 rounded-full animate-ping hidden group-hover:block"
                        style="animation-delay: 0.3s;"></div>
                </div>
                <div class="relative group cursor-pointer transition-all duration-500 ">
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 rounded-3xl blur-sm group-hover:blur-md transition-all duration-500 ">
                    </div>
                    <div
                        class="relative bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 rounded-3xl p-[2px] transform transition-all duration-500 md:h-full">
                        <div class="bg-white/95 backdrop-blur-sm rounded-3xl p-8 h-full">
                            <div class="flex justify-between items-start  md:mb-6 mb-2">
                                <div
                                    class="w-16 h-16 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-6">
                                    <div class="text-white">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-utensils h-8 w-8">
                                            <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
                                            <path d="M7 2v20"></path>
                                            <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div
                                    class="px-3 py-1 rounded-full text-orange-600 bg-white border-2 border-current text-xs font-bold">
                                    Buffet</div>
                            </div>

                            <h3 class="md:text-2xl text-xl font-bold mb-4 text-gray-800">
                                Đồ ăn, thức uống phong phú miễn phí</h3>
                            <p class="text-gray-600 leading-relaxed mb-6">Hành khách được phục vụ buffet tự chọn với
                                nhiều món ăn nóng – lạnh, đồ Âu – Á, đồ ăn nhẹ, bánh ngọt, trái cây, cùng với nước uống
                                như cà phê, nước ép</p>
                        </div>
                    </div>
                    <div
                        class="absolute top-4 right-4 w-3 h-3 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 rounded-full animate-ping hidden group-hover:block">
                    </div>
                    <div class="absolute top-8 right-8 w-2 h-2 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 rounded-full animate-ping hidden group-hover:block"
                        style="animation-delay: 0.3s;"></div>
                </div>
                <div class="relative group cursor-pointer transition-all duration-500 ">
                    <div
                        class="absolute inset-0 bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 rounded-3xl blur-sm group-hover:blur-md transition-all duration-500  ">
                    </div>
                    <div
                        class="relative bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 rounded-3xl p-[2px] transform transition-all duration-500">
                        <div class="bg-white/95 backdrop-blur-sm rounded-3xl p-8 h-full">
                            <div class="flex justify-between items-start md:mb-6 mb-2">
                                <div
                                    class="w-16 h-16 bg-gradient-to-br from-purple-400 via-violet-400 to-fuchsia-400 rounded-2xl flex items-center justify-center mb-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-6">
                                    <div class="text-white">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-shield h-8 w-8">
                                            <path
                                                d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z">
                                            </path>
                                        </svg>
                                    </div>
                                </div>
                                <div
                                    class="px-3 py-1 rounded-full text-purple-600 bg-white border-2 border-current text-xs font-bold">
                                    24/7</div>
                            </div>

                            <h3 class="md:text-2xl text-xl font-bold mb-4 text-gray-800">Dịch vụ cao cấp & tiện nghi đặc
                                biệt</h3>
                            <p class="text-gray-600 leading-relaxed mb-6"> Các tiện ích đi kèm như phòng tắm riêng
                                , massage, khu vực ngủ ngắn, tạp chí, Wi-Fi tốc độ cao, màn hình thông tin
                                chuyến bay và hỗ trợ hành khách khi có thay đổi lịch bay</p>

                        </div>
                    </div>
                    <div
                        class="absolute top-4 right-4 w-3 h-3 bg-gradient-to-br from-purple-400 via-violet-400 to-fuchsia-400 rounded-full animate-ping hidden group-hover:block">
                    </div>
                    <div class="absolute top-8 right-8 w-2 h-2 bg-gradient-to-br from-purple-400 via-violet-400 to-fuchsia-400 rounded-full animate-ping hidden group-hover:block"
                        style="animation-delay: 0.3s;"></div>
                </div>
            </div>

        </div>
    </section>
    <div>
        <!-- Start Miền Bắc -->
        <section #northContainer class="py-20 relative overflow-hidden max-md:mx-2">
            <!-- <div class="absolute inset-0 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50"></div> -->
            <div class="absolute inset-0 opacity-60">
                <!-- <div
                    class="absolute inset-0 bg-gradient-to-r from-emerald-100/80 via-transparent to-teal-100/80 animate-pulse">
                </div> -->
                <div class="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-100/60 to-transparent animate-pulse"
                    style="animation-delay: 1s;"></div>
            </div>
            <div
                class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-emerald-200/30 to-teal-300/30 rounded-full blur-3xl animate-float">
            </div>
            <div class="absolute top-20 right-20 w-64 h-64 bg-gradient-to-br from-teal-200/40 to-cyan-300/40 rounded-full blur-2xl animate-float"
                style="animation-delay: 2s;"></div>
            <div class="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-cyan-200/35 to-emerald-300/35 rounded-full blur-3xl animate-float"
                style="animation-delay: 1s;"></div>
            <div class="absolute top-20 right-20 animate-float">
                <div
                    class="w-16 h-16 bg-gradient-to-br from-emerald-400/20 to-teal-400/20 rounded-2xl backdrop-blur-sm border border-white/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plane h-8 w-8 text-emerald-500">
                        <path
                            d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                        </path>
                    </svg>
                </div>
            </div>
            <div class="absolute bottom-32 left-16 animate-float" style="animation-delay: 1s;">
                <div
                    class="w-12 h-12 bg-gradient-to-br from-teal-400/20 to-cyan-400/20 rounded-xl backdrop-blur-sm border border-white/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-coffee h-6 w-6 text-teal-500">
                        <path d="M10 2v2"></path>
                        <path d="M14 2v2"></path>
                        <path
                            d="M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1">
                        </path>
                        <path d="M6 2v2"></path>
                    </svg>
                </div>
            </div>
            <div class="max-w-6xl mx-auto relative z-10">
                <div class="flex justify-center mb-12">
                    <div class="relative group">
                        <div
                            class="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-full blur-sm group-hover:blur-md transition-all duration-500 animate-pulse">
                        </div>
                        <div
                            class="relative px-8 py-4 rounded-full bg-white/90 backdrop-blur-sm shadow-2xl flex items-center gap-3 border border-white/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="lucide lucide-plane h-6 w-6 text-emerald-600 animate-bounce">
                                <path
                                    d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                </path>
                            </svg>
                            <span
                                class="bg-gradient-to-r text-center from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent font-bold text-base md:text-lg">Miền
                                Bắc - Phòng Chờ Cao Cấp</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="lucide lucide-sparkles h-5 w-5 text-teal-500 animate-spin"
                                style="animation-duration: 3s;">
                                <path
                                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z">
                                </path>
                                <path d="M20 3v4"></path>
                                <path d="M22 5h-4"></path>
                                <path d="M4 17v2"></path>
                                <path d="M5 18H3"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="text-center mb-16">
                    <h2
                        class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent animate-fade-in md:pb-2">
                        Phòng Chờ Thủ Đô</h2>
                    <p class="text-base sm:text-lg md:text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed animate-fade-in px-4 sm:px-0"
                        style="animation-delay: 0.2s;">Trải nghiệm dịch vụ phòng chờ đẳng cấp tại các sân bay miền Bắc
                        với tiện nghi hiện đại và dịch vụ chuyên nghiệp ✈️</p>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
                    <div class="space-y-8">
                        <div class="flex items-center gap-4 mb-8">
                            <h3
                                class="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                                Tiện Nghi Cao Cấp</h3>
                            <div class="flex-1 h-px bg-gradient-to-r from-emerald-300 to-transparent"></div>
                        </div>
                        <div class="space-y-4">
                            <div class="group relative p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 mb-6 overflow-hidden border border-white/20"
                                style="animation-delay: 100ms;">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse">
                                </div>
                                <div class="absolute inset-[1px] bg-white rounded-2xl"></div>
                                <div class="absolute top-2 right-2 w-2 h-2 bg-emerald-400 rounded-full animate-bounce"
                                    style="animation-delay: 0s;"></div>
                                <div class="absolute top-4 right-6 w-1 h-1 bg-teal-400 rounded-full animate-bounce"
                                    style="animation-delay: 0.5s;"></div>
                                <div class="relative z-10 flex items-start gap-4">
                                    <div
                                        class="p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-teal-100 transition-transform duration-300 ">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-utensils h-6 w-6 text-emerald-600">
                                            <path d="M18 20a6 6 0 0 0-12 0"></path>
                                            <circle cx="12" cy="10" r="4"></circle>
                                            <circle cx="12" cy="12" r="10"></circle>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4
                                            class="font-semibold mb-2 text-gray-800 group-hover:text-emerald-600 transition-colors">
                                            Dịch vụ cá nhân hóa & không gian riêng tư</h4>
                                        <p class="text-gray-600 text-sm leading-relaxed">Với phương châm "Service from
                                            the Heart", SH Elite Lounge mang đến trải nghiệm thư giãn trước chuyến bay
                                            thông qua dịch vụ được cá nhân hóa, không gian yên tĩnh và tinh tế – lý
                                            tưởng cho hành khách cần nghỉ ngơi hoặc làm việc.</p>
                                    </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-arrow-right h-5 w-5 text-emerald-500 transition-transform duration-300 ">
                                        <path d="M5 12h14"></path>
                                        <path d="m12 5 7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="group relative p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 mb-6 overflow-hidden border border-white/20"
                                style="animation-delay: 200ms;">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse">
                                </div>
                                <div class="absolute inset-[1px] bg-white rounded-2xl"></div>
                                <div class="absolute top-2 right-2 w-2 h-2 bg-emerald-400 rounded-full animate-bounce"
                                    style="animation-delay: 0s;"></div>
                                <div class="absolute top-4 right-6 w-1 h-1 bg-teal-400 rounded-full animate-bounce"
                                    style="animation-delay: 0.5s;"></div>
                                <div class="relative z-10 flex items-start gap-4">
                                    <div
                                        class="p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-teal-100 transition-transform duration-300 ">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-utensils h-6 w-6 text-emerald-600">
                                            <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
                                            <path d="M7 2v20"></path>
                                            <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4
                                            class="font-semibold mb-2 text-gray-800 group-hover:text-emerald-600 transition-colors">
                                            Buffet Ẩm Thực Cao Cấp</h4>
                                        <p class="text-gray-600 text-sm leading-relaxed">Thưởng thức buffet đa dạng với
                                            các món ăn Việt Nam và quốc tế, đồ uống không giới hạn</p>
                                    </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-arrow-right h-5 w-5 text-emerald-500 transition-transform duration-300 ">
                                        <path d="M5 12h14"></path>
                                        <path d="m12 5 7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="group relative p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 mb-6 overflow-hidden border border-white/20"
                                style="animation-delay: 300ms;">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse">
                                </div>
                                <div class="absolute inset-[1px] bg-white rounded-2xl"></div>
                                <div class="absolute top-2 right-2 w-2 h-2 bg-emerald-400 rounded-full animate-bounce"
                                    style="animation-delay: 0s;"></div>
                                <div class="absolute top-4 right-6 w-1 h-1 bg-teal-400 rounded-full animate-bounce"
                                    style="animation-delay: 0.5s;"></div>
                                <div class="relative z-10 flex items-start gap-4">
                                    <div
                                        class="p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-teal-100 transition-transform duration-300 ">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-shield h-6 w-6 text-emerald-600">
                                            <path
                                                d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z">
                                            </path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4
                                            class="font-semibold mb-2 text-gray-800 group-hover:text-emerald-600 transition-colors">
                                            Dịch Vụ Hỗ Trợ 24/7</h4>
                                        <p class="text-gray-600 text-sm leading-relaxed">Đội ngũ nhân viên chuyên nghiệp
                                            hỗ trợ check-in, thông tin chuyến bay và các dịch vụ khác</p>
                                    </div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-arrow-right h-5 w-5 text-emerald-500 transition-transform duration-300 ">
                                        <path d="M5 12h14"></path>
                                        <path d="m12 5 7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <a href="tel:02822293030"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary hover:bg-primary/90 h-10 group bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-600 text-white px-8 py-4 rounded-2xl text-lg font-semibold shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">Đặt
                            phòng chờ ngay<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-arrow-right ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                    <div class="relative">
                        <div class="relative group max-md:px-2">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-500 animate-pulse">
                            </div>
                            <div
                                class="relative bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-3xl p-1 transform group-hover:scale-105 transition-transform duration-500">
                                <div class="bg-white rounded-3xl overflow-hidden">
                                    <app-slider-images [bufferSize]="1" [navigationDots]="true"
                                        [items]="regions.north.slider"></app-slider-images>
                                </div>
                            </div>
                            <div
                                class="absolute -bottom-6 -right-6 px-6 py-4 rounded-2xl bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 text-white shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300 border border-white/20 max-sm:bottom-0 max-sm:right-0 max-sm:px-4 max-sm:py-3">
                                <div class="text-center"><span class="text-xl font-bold block max-sm:text-lg">Hà Nội
                                        &amp; Hải
                                        Phòng</span><span class="text-sm opacity-90 max-sm:text-xs">Nội Bài &amp; Cát Bi
                                        Airport</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <app-slider-products [items]="regions.north.items" [color]="'emerald'"></app-slider-products>
            </div>

        </section>
        <!-- End Miền Bắc -->

        <!-- Start miền Trung -->
        <section #centralContainer class="py-20 relative overflow-hidden max-md:mx-2">
            <!-- <div class="absolute inset-0 bg-gradient-to-br from-orange-50 via-red-50 to-pink-50"></div> -->
            <div class="absolute inset-0 opacity-70">
                <!-- <div
                    class="absolute inset-0 bg-gradient-to-r from-orange-100/60 via-transparent to-red-100/60 animate-pulse">
                </div> -->
                <div class="absolute inset-0 bg-gradient-to-b from-transparent via-pink-100/50 to-transparent animate-pulse"
                    style="animation-delay: 1.5s;">

                </div>
            </div>
            <div
                class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-orange-200/40 to-red-300/40 rounded-full blur-3xl animate-float">

            </div>
            <div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-br from-pink-200/30 to-rose-300/30 rounded-full blur-3xl animate-float"
                style="animation-delay: 1s;">

            </div>
            <div class="absolute top-16 left-20 animate-float">
                <div
                    class="w-20 h-20 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-3xl backdrop-blur-sm border border-white/30 flex items-center justify-center transform rotate-12">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plane h-10 w-10 text-orange-500">
                        <path
                            d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                        </path>
                    </svg>
                </div>
            </div>
            <div class="absolute bottom-20 right-16 animate-float" style="animation-delay: 1s;">
                <div
                    class="w-16 h-16 bg-gradient-to-br from-red-400/20 to-pink-400/20 rounded-2xl backdrop-blur-sm border border-white/30 flex items-center justify-center transform -rotate-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-coffee h-8 w-8 text-red-500">
                        <path d="M10 2v2">

                        </path>
                        <path d="M14 2v2">

                        </path>
                        <path
                            d="M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1">

                        </path>
                        <path d="M6 2v2">

                        </path>
                    </svg>
                </div>
            </div>
            <div class="max-w-6xl mx-auto relative z-10">
                <div class="text-center mb-16">
                    <div class="relative inline-block mb-8 group">
                        <div
                            class="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 rounded-full blur-sm group-hover:blur-md  animate-pulse">
                        </div>
                        <div
                            class="relative px-8 py-4 rounded-full bg-white/90 backdrop-blur-sm shadow-2xl flex items-center gap-3 border border-white/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="lucide lucide-plane h-6 w-6 text-orange-600 animate-bounce">
                                <path
                                    d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                </path>
                            </svg>
                            <span
                                class="bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent font-bold text-lg">Miền
                                Trung - Nơi dừng chân thư giãn</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="lucide lucide-sparkles h-5 w-5 text-red-500 animate-pulse">
                                <path
                                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z">
                                </path>
                                <path d="M20 3v4"></path>
                                <path d="M22 5h-4"></path>
                                <path d="M4 17v2"></path>
                                <path d="M5 18H3"></path>
                            </svg>
                        </div>
                    </div>
                    <h2
                        class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent animate-fade-in pb-2">
                        Phòng Chờ Miền Trung</h2>
                    <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed animate-fade-in"
                        style="animation-delay: 0.2s;">Thưởng thức không gian phòng chờ cao cấp, nơi hội tụ tinh hoa văn
                        hóa bản địa và tiện nghi hiện đại 🌟</p>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start mb-16">
                    <div class="lg:col-span-2 relative group">
                        <div class="relative">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 rounded-3xl blur-lg group-hover:blur-xl transition-all duration-500 animate-pulse">
                            </div>
                            <div
                                class="relative bg-gradient-to-r max-md:mx-2 from-orange-400 via-red-400 to-pink-400 rounded-3xl p-1 transform group-hover:scale-105 transition-transform duration-500">
                                <div class="bg-white rounded-3xl overflow-hidden">
                                    <app-slider-images [bufferSize]="1" [navigationDots]="true"
                                        [items]="regions.central.slider"></app-slider-images>
                                    <div
                                        class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent rounded-3xl">
                                    </div>
                                    <div class="absolute bottom-8 left-8 text-white">

                                        <a href="tel:02822293030"
                                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary hover:bg-primary/90 h-10 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 hover:from-orange-600 hover:via-red-600 hover:to-pink-600 text-white border-0 px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                                            Đặt phòng chờ ngay
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-chevron-right h-5 w-5 ml-1">
                                                <path d="m9 18 6-6-6-6"></path>
                                            </svg>
                                        </a>
                                    </div>
                                    <div
                                        class="absolute top-8 right-8 px-4 sm:px-6 py-2 sm:py-3 rounded-xl bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white shadow-xl border border-white/20 transform hover:scale-110 transition-transform duration-300 max-sm:top-4 max-sm:right-4">
                                        <span class="text-base sm:text-lg font-bold">Nha Trang &amp; Đà Lạt ✈️</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-6">
                        <div class="relative group cursor-pointer">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-red-400 to-pink-400 rounded-2xl blur-sm group-hover:blur-md transition-all duration-500 ">
                            </div>
                            <div
                                class="relative bg-gradient-to-r from-red-400 to-pink-400 rounded-2xl p-[1px] transform transition-all duration-300 ">
                                <div class="bg-white/95 backdrop-blur-sm rounded-2xl p-6 h-full">
                                    <!-- <div
                                        class="bg-gradient-to-br from-orange-100 to-red-100 rounded-xl p-4 w-fit mb-4 transition-transform duration-300 ">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-coffee h-7 w-7 text-orange-600">
                                            <path d="M10 2v2"></path>
                                            <path d="M14 2v2"></path>
                                            <path
                                                d="M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1">
                                            </path>
                                            <path d="M6 2v2"></path>
                                        </svg>
                                    </div> -->
                                    <h4 class="font-bold mb-3 text-gray-800 text-lg">Tiện nghi hiện đại & dịch vụ chu
                                        đáo</h4>
                                    <p class="text-gray-600 text-sm leading-relaxed">Wi-Fi tốc độ cao, ghế ngồi thoải
                                        mái, đồ ăn nhẹ, đồ uống, báo chí và tạp chí – tất cả sẵn sàng phục vụ hành khách
                                        trong một không gian tiện nghi và thư giãn.</p>

                                </div>
                            </div>
                        </div>
                        <div class="relative group cursor-pointer">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-pink-400 to-rose-400 rounded-2xl blur-sm group-hover:blur-md transition-all duration-500 ">
                            </div>
                            <div
                                class="relative bg-gradient-to-r from-pink-400 to-rose-400 rounded-2xl p-[1px] transform transition-all duration-300 ">
                                <div class="bg-white/95 backdrop-blur-sm rounded-2xl p-6 h-full">
                                    <!-- <div
                                        class="bg-gradient-to-br from-orange-100 to-red-100 rounded-xl p-4 w-fit mb-4 transition-transform duration-300 ">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-clock h-7 w-7 text-orange-600">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg>
                                    </div> -->
                                    <h4 class="font-bold mb-3 text-gray-800 text-lg">Khung cảnh sân bay từ cửa sổ lounge
                                    </h4>
                                    <p class="text-gray-600 text-sm leading-relaxed">Những ô cửa kính lớn mở ra khung
                                        cảnh sân bay sống động, nơi hành khách có thể ngắm máy bay cất, hạ cánh – mang
                                        đến một trải nghiệm độc đáo và ấn tượng.</p>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <app-slider-products [items]="regions.central.items" [color]="'orange'"></app-slider-products>

            </div>

        </section>
        <!-- End Miền Trung -->

        <!-- Start Miền Nam -->
        <section #southContainer class="py-20 relative overflow-hidden max-md:mx-2">
            <!-- <div class="absolute inset-0 bg-gradient-to-br from-purple-50 via-violet-50 to-fuchsia-50"></div> -->
            <div class="absolute inset-0 opacity-60">
                <!-- <div
                    class="absolute inset-0 bg-gradient-to-r from-purple-100/70 via-transparent to-violet-100/70 animate-pulse">
                </div> -->
                <div class="absolute inset-0 bg-gradient-to-b from-transparent via-fuchsia-100/50 to-transparent animate-pulse"
                    style="animation-delay: 2s;"></div>
            </div>
            <div
                class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-200/40 to-violet-300/40 rounded-full blur-3xl animate-float">
            </div>
            <div class="absolute bottom-0 right-0 w-88 h-88 bg-gradient-to-br from-fuchsia-200/30 to-pink-300/30 rounded-full blur-3xl animate-float"
                style="animation-delay: 2.5s;"></div>
            <div class="absolute top-20 left-16 animate-float">
                <div
                    class="w-24 h-24 bg-gradient-to-br from-purple-400/20 to-violet-400/20 rounded-full backdrop-blur-sm border border-white/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plane h-12 w-12 text-purple-500">
                        <path
                            d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                        </path>
                    </svg>
                </div>
            </div>
            <div class="absolute bottom-32 right-20 animate-float" style="animation-delay: 1s;">
                <div
                    class="w-18 h-18 bg-gradient-to-br from-violet-400/20 to-fuchsia-400/20 rounded-2xl backdrop-blur-sm border border-white/30 flex items-center justify-center transform rotate-12">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-map-pin h-8 w-8 text-violet-400">
                        <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                </div>
            </div>
            <div class="max-w-6xl mx-auto relative z-10">
                <div class="flex flex-col lg:flex-row items-center gap-16 mb-20">
                    <div class="lg:w-1/2 space-y-8">
                        <div class="relative inline-block group">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 rounded-full blur-sm group-hover:blur-md animate-pulse">
                            </div>
                            <div
                                class="relative px-8 py-4 rounded-full bg-white/90 backdrop-blur-sm shadow-2xl flex items-center gap-3 border border-white/30">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="lucide lucide-plane h-6 w-6 text-purple-600 animate-bounce">
                                    <path
                                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z">
                                    </path>
                                </svg>
                                <span
                                    class="bg-gradient-to-r from-purple-600 via-violet-600 to-fuchsia-600 bg-clip-text text-transparent font-bold text-lg">Miền
                                    Nam - Phòng Chờ Hiện Đại</span><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-map-pin h-5 w-5 text-violet-500 animate-pulse">
                                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                    <circle cx="12" cy="10" r="3"></circle>
                                </svg>
                            </div>
                        </div>
                        <h2
                            class="text-5xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-violet-600 to-fuchsia-600 bg-clip-text text-transparent animate-fade-in pb-2">
                            Phòng Chờ Sài Gòn</h2>
                        <p class="text-xl text-gray-700 leading-relaxed animate-fade-in" style="animation-delay: 0.2s;">
                            Trải nghiệm dịch vụ phòng chờ đẳng cấp quốc tế tại TP.HCM và Phú Quốc với tiện nghi hiện đại
                            và dịch vụ chuyên nghiệp 🌴</p>
                        <a href="tel:02822293030"
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50  bg-primary hover:bg-primary/90 h-10 group bg-gradient-to-r from-purple-500 via-violet-500 to-fuchsia-500 hover:from-purple-600 hover:via-violet-600 hover:to-fuchsia-600 text-white px-8 py-4 rounded-2xl text-lg font-bold shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 animate-fade-in"
                            style="animation-delay: 0.4s;">Đặt phòng chờ ngay<svg xmlns="http://www.w3.org/2000/svg"
                                width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-arrow-right ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                    <div class="lg:w-1/2 relative">
                        <div class="relative group  max-md:mx-2">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 animate-pulse">
                            </div>
                            <div class="relative bg-gradient-to-r from-purple-400 via-violet-400 to-fuchsia-400 rounded-3xl p-1 transform group-hover:rotateY-12 group-hover:scale-105 transition-all duration-700 overflow-hidden"
                                style="transform-style: preserve-3d;">
                                <div class="bg-white rounded-3xl overflow-hidden">
                                    <app-slider-images [bufferSize]="1" [navigationDots]="true"
                                        [items]="regions.south.slider"></app-slider-images>
                                </div>
                            </div>
                            <div
                                class="absolute -bottom-8 -left-8 px-4 sm:px-8 py-4 sm:py-6 rounded-2xl bg-gradient-to-r from-purple-500 via-violet-500 to-fuchsia-500 text-white shadow-2xl transform rotate-6 hover:rotate-0 transition-all duration-500 border border-white/20 group-hover:scale-110 max-sm:bottom-0 max-sm:left-0 ">
                                <div class="text-center">
                                    <span class="text-lg sm:text-2xl font-bold block">TP.HCM &amp; Phú Quốc</span>
                                    <span class="text-xs sm:text-sm opacity-90">Tân Sơn Nhất &amp; Phú Quốc
                                        Airport</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="space-y-12 mb-16">
                    <div class="text-center">
                        <h3
                            class="text-4xl font-bold bg-gradient-to-r from-purple-600 via-violet-600 to-fuchsia-600 bg-clip-text text-transparent mb-4">
                            Tiện Nghi Cao Cấp</h3>
                        <div class="w-24 h-1 bg-gradient-to-r from-purple-400 to-fuchsia-400 mx-auto rounded-full">
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="relative group cursor-pointer transition-all duration-500 ">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-purple-400 via-violet-400 to-indigo-400 rounded-2xl blur-sm group-hover:blur-md transition-all duration-500 ">
                            </div>
                            <div
                                class="relative bg-gradient-to-r from-purple-400 via-violet-400 to-indigo-400 rounded-2xl p-[2px] transform transition-all duration-500">
                                <div class="bg-white/95 backdrop-blur-sm rounded-2xl overflow-hidden h-full">

                                    <div class="p-6">
                                        <h4 class="font-bold mb-3 text-gray-800 text-xl">Dịch vụ spa & ghế massage tự
                                            động</h4>
                                        <p class="text-gray-600 text-sm leading-relaxed mb-4">Trang bị ghế massage toàn
                                            thân hiện đại và dịch vụ spa nhẹ nhàng, lounge mang đến sự thư giãn tối đa
                                            trước hành trình bay.</p>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="relative group cursor-pointer transition-all duration-500 ">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-violet-400 via-purple-400 to-fuchsia-400 rounded-2xl blur-sm group-hover:blur-md transition-all duration-500 ">
                            </div>
                            <div
                                class="relative bg-gradient-to-r from-violet-400 via-purple-400 to-fuchsia-400 rounded-2xl p-[2px] transform transition-all duration-500">
                                <div class="bg-white/95 backdrop-blur-sm rounded-2xl overflow-hidden h-full">

                                    <div class="p-6">
                                        <h4 class="font-bold mb-3 text-gray-800 text-xl">Tiện ích công việc</h4>
                                        <p class="text-gray-600 text-sm leading-relaxed mb-4">Wi-Fi tốc độ cao, ổ cắm đa
                                            chuẩn, máy in/scan miễn phí và khu vực làm việc yên tĩnh – tất cả được thiết
                                            kế để hỗ trợ hành khách công tác một cách hiệu quả và tiện lợi.</p>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="relative group cursor-pointer transition-all duration-500 ">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-fuchsia-400 via-pink-400 to-rose-400 rounded-2xl blur-sm group-hover:blur-lg transition-all duration-500 ">
                            </div>
                            <div
                                class="relative bg-gradient-to-r from-fuchsia-400 via-pink-400 to-rose-400 rounded-2xl p-[2px] transform transition-all duration-500 md:h-full">
                                <div class="bg-white/95 backdrop-blur-sm rounded-2xl overflow-hidden h-full">

                                    <div class="p-6">
                                        <h4 class="font-bold mb-3 text-gray-800 text-xl">Ẩm Thực Đặc Sắc</h4>
                                        <p class="text-gray-600 text-sm leading-relaxed mb-4">Buffet phong phú với đặc
                                            sản miền Nam, quầy bar cocktail và dịch vụ room service cao cấp</p>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <app-slider-products [items]="regions.south.items" [color]="'purple'"></app-slider-products>

            </div>

        </section>
        <!-- End Miền Nam -->
    </div>

    <app-bsl></app-bsl>
</main>



<app-advantage></app-advantage>