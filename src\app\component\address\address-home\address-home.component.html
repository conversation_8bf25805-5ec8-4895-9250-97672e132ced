<app-address-banner></app-address-banner>

<div class="relative">
    <div class="relative px-[10%] max-md:px-2 z-20">
        <div class="w-full">
            <div class="w-full">
                <h2 class="pt-6 text-[#0f294d] dark:text-gray-200 text-3xl font-bold text-center"><PERSON><PERSON><PERSON><PERSON></h2>
            </div>
            <div class="w-full mb-8">
                <ul class="flex justify-center gap-4 mt-4 type-address">
                    <li [class.active]="isTypeActive('inCountry')" (click)="clickType('inCountry')"
                        class="list-none cursor-pointer px-4 py-2 bg-gray-50 dark:bg-transparent dark:text-gray-200 font-semibold text-[#333]">
                        Trong Nước
                    </li>
                    <li [class.active]="isTypeActive('foreignCountry')" (click)="clickType('foreignCountry')"
                        class="list-none cursor-pointer px-4 py-2 bg-gray-50 dark:bg-transparent dark:text-gray-200 font-semibold text-[#333]">
                        <PERSON><PERSON><PERSON><PERSON>i
                    </li>
                </ul>
            </div>
            <div class="w-full">
                @if(dataResult.items.length == 0){
                <div
                    class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
                    <h1 class="text-xl font-bold tracking-tight text-gray-900 dark:text-white">Kết quả tìm kiếm</h1>
                    <h3>Đã tìm thấy <span class="text-primary-600 font-bold">0</span> kết quả</h3>
                </div>
                }@else{
                <div
                    class="mt-4 grid grid-cols-2 gap-4 max-sm:gap-1 mx-auto text-gray-900 sm:grid-cols-3 xl:grid-cols-4 dark:text-white w-full overflow-x-auto">

                    @for(item of dataResult.items; track item){
                    <div>
                        <div class="border border-[#ccc] rounded shadow bg-white p-0 relative group"
                            routerLink="/address/{{item.seoTitle}}">
                            <div class="w-full h-[200px] rounded cursor-pointer overflow-hidden">
                                <i
                                    class="w-full h-full absolute rounded-[1.4px] z-10 bg-gradient-to-b from-[#0f294d00] to-[#0f294d80]"></i>
                                <img src="{{item.image}}" alt="" loading="lazy"
                                    class="w-full h-full rounded group-hover:scale-110 transition-all duration-500 ease-in-out">
                            </div>
                            <div class="mt-4 py-0 px-4 absolute bottom-4 justify-center flex w-full z-20">
                                <h3 class="text-white">{{item.name}}</h3>
                            </div>
                        </div>
                    </div>
                    }
                </div>
                }
                <div class="w-full">
                    <div *ngIf="dataResult.pageCount > 1">
                        <nav class="flex items-center flex-column flex-wrap md:flex-row justify-between p-4"
                            aria-label="Table navigation">
                            <span
                                class="text-sm font-normal text-gray-500 dark:text-gray-400 mb-4 md:mb-0 block w-full md:inline md:w-auto">Showing
                                <span class="font-semibold text-gray-900 dark:text-white">{{dataResult?.from}} -
                                    {{dataResult?.to}}</span> of <span
                                    class="font-semibold text-gray-900 dark:text-white">{{dataResult?.totalRecords}}</span></span>
                            <ul class="inline-flex -space-x-px rtl:space-x-reverse text-sm h-8">
                                <li [ngClass]="{disabled:dataResult.pageIndex === 1}">
                                    <a (click)="getUrl(1)"
                                        class="cursor-pointer flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                        <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                            viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m17 16-4-4 4-4m-6 8-4-4 4-4" />
                                        </svg>
                                    </a>
                                </li>
                                <li [ngClass]="{disabled:dataResult.pageIndex === 1}">
                                    <a (click)="getUrl(dataResult.pageIndex - 1)"
                                        class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                        <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                            viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m14 8-4 4 4 4" />
                                        </svg>
                                    </a>
                                </li>

                                <li *ngFor="let page of range(startIndex, finishIndex); let i = index"
                                    [class.active]="page === dataResult.pageIndex">
                                    <a class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                                        (click)="getUrl(page)" [title]="'Page ' + page">
                                        {{ page }}
                                    </a>
                                </li>

                                <li [ngClass]="{disabled:dataResult.pageIndex === dataResult.pageCount}">
                                    <a (click)="getUrl(dataResult.pageIndex + 1)"
                                        class="cursor-pointer flex items-center justify-center px-1 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                        <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                            viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m10 16 4-4-4-4" />
                                        </svg>
                                    </a>
                                </li>

                                <li [ngClass]="{disabled:dataResult.pageIndex === dataResult.pageCount}">
                                    <a (click)="getUrl(dataResult.pageCount)"
                                        class="cursor-pointer flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                        <svg class="w-6 h-6 text-gray-500 dark:text-white" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                            viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m7 16 4-4-4-4m6 8 4-4-4-4" />
                                        </svg>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

            </div>
        </div>

        <div class="mb-16">
            <div class="w-full">
                <h2 class="pt-16 text-[#0f294d] text-3xl font-bold text-center mb-5 dark:text-gray-200">Kinh Nghiệm du
                    lịch</h2>
            </div>
            <div class="w-full">
                @if(newsDefault.length == 0){
                <div
                    class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
                    <h1 class="text-xl font-bold tracking-tight text-gray-900 dark:text-white">Kết quả tìm kiếm</h1>
                    <h3>Đã tìm thấy <span class="text-primary-600 font-bold">0</span> kết quả</h3>
                </div>
                }@else{
                <div
                    class="cursor-pointer mt-4 grid grid-cols-2 gap-4 max-sm:gap-1 mx-auto text-gray-900 sm:grid-cols-3 xl:grid-cols-4 dark:text-white w-full overflow-x-auto">
                    @for(item of newsDefault; track item){
                    <div routerLink="/news/{{item.seoCategory}}/{{item.seoTitle}}"
                        class="max-w-sm bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
                        <div class="overflow-hidden rounded">
                            <img class="w-full h-[200px] object-cover rounded-t hover:scale-110 transition-all duration-500 ease-in-out"
                                src="{{item.pathImage}}" alt="{{item.name}}" />
                        </div>
                        <div class="p-4">
                            <a>
                                <h5
                                    class="mb-2 text-base font-bold tracking-tight text-gray-900 dark:text-white line-clamp-2 h-12">
                                    {{item.name}}</h5>
                            </a>
                            <p class="font-normal text-sm text-gray-700 dark:text-gray-400 line-clamp-3 h-[3.75rem]">
                                {{item.description}}</p>

                        </div>
                    </div>
                    }
                </div>
                }

            </div>
        </div>
    </div>
</div>
<app-email-contact></app-email-contact>