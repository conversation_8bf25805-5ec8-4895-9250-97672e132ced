import { Component, Input, Inject, PLATFORM_ID, ElementRef, ViewChild, AfterViewInit, OnInit } from '@angular/core';
import { LoadingSkeletonComponent } from '../../loading-skeleton/loading-skeleton.component';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { RouterLink } from '@angular/router';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { AddressService } from '../../../service/address/address.service';
import { TranslateService } from '@ngx-translate/core';
import { ScreenService } from '../../../service/screen/screen.service';
import { PagingRequest } from '../../../interface/paging-request';

@Component({
  selector: 'app-address-common',
  imports: [
    SlickCarouselModule,
    RouterLink,
    CommonModule,
    LoadingSkeletonComponent
  ],
  templateUrl: './address-common.component.html',
  styleUrl: './address-common.component.css'
})

export class AddressCommonComponent implements AfterViewInit, OnInit {
  isVisible = false;
  isLoading = true;
  dataLoaded = false; // Flag để tránh load data nhiều lần
  provinces: any = [];
  dataAddress: any;
  translatePage: any;

  @ViewChild('addressSection') addressSection!: ElementRef;

  // name title show in ui
  _title: string = 'Điểm Đến Nổi Bật';
  // category tour load api
  _type: string = "all";
  // show category tour
  _showCategory: boolean = true;
  // quantity items load api
  _quantity: number = 10;
  _animation: string = 'none'; //data-aos="fade-up"
  _showNote: boolean = false;
  _showAdvantage: boolean = false;
  _loadDistance: string = '400px'; // Khoảng cách để bắt đầu load data

  @Input()
  set loadDistance(value: string) {
    this._loadDistance = value;
  }
  get loadDistance(): string {
    return this._loadDistance;
  }

  @Input()
  set showAdvantage(value: boolean) {
    this._showAdvantage = value;
  }
  get showAdvantage(): boolean {
    return this._showAdvantage;
  }
  @Input()
  set title(value: string) {
    this._title = value;
  }
  get title(): string {
    return this._title;
  }
  @Input()
  set showNote(value: boolean) {
    this._showNote = value;
  }
  get showNote(): boolean {
    return this._showNote;
  }

  @Input()
  set showCategory(value: boolean) {
    this._showCategory = value;
  }
  get showCategory(): boolean {
    return this._showCategory;
  }

  @Input()
  set type(value: string) {
    this._type = value;
  }
  get type(): string {
    return this._type;
  }

  @Input()
  set quantity(value: number) {
    this._quantity = value;
  }
  get quantity(): number {
    return this._quantity;
  }

  @Input()
  set animation(value: string) {
    this._animation = value;
  }
  get animation(): string {
    return this._animation;
  }

  constructor(
    private addressService: AddressService,
    private translate: TranslateService,
    private readonly screenService: ScreenService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
  }

  async ngOnInit() {
    var lang = 'vi';
    if (isPlatformBrowser(this.platformId)) {
      lang = localStorage.getItem('lang') || 'vi';
    }
    this.translate.use(lang);
    await this.loadTransLate(this.translate.currentLang);

    this.translate.onLangChange.subscribe(() => {
      this.loadTransLate(this.translate.currentLang);
    });
  }

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.setupIntersectionObserver();
    }
  }

  private setupIntersectionObserver(): void {
    const options = {
      root: null,
      rootMargin: this._loadDistance,
      threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.isVisible) {
          this.isVisible = true;
          this.onVisible();
          observer.unobserve(entry.target);
        }
      });
    }, options);

    if (this.addressSection) {
      observer.observe(this.addressSection.nativeElement);
    }
  }
  async onVisible() {
    if (this.dataLoaded) {
      console.log('Data already loaded, skipping...');
      return;
    }

    this.isLoading = true;
    console.log('Component near viewport, starting to load data...');
    
    try {
      // Load data song song để tối ưu performance
      await Promise.all([
        this.loadProvinces(this._quantity),
        this.loadHotAddress()
      ]);
      
      this.dataLoaded = true;
      console.log('Data loaded successfully');
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async loadTransLate(lang: string = 'vi') {
    var request = {
      Language: lang,
      ScreenCode: 'S0004-0025'
    }
    const observable = await this.screenService.getPageTranslate(request);
    const res = await observable.toPromise();
    try {
      if (res.isSuccessed) {
        this.translatePage = res.resultObj;
        this._title = this.translatePage?.['S0004-0025_0001'];
      }
    } catch (error) {
      console.log(error);
    }
  }

  async loadProvinces(quantity: number) {
    console.log('Loading provinces...');
    try {
      const res = await this.addressService.getProvinces(quantity).toPromise();
      if (res && res.resultObj) {
        this.provinces = res.resultObj;
        console.log('Provinces loaded:', this.provinces.length);
      }
    } catch (error) {
      console.error('Error loading provinces:', error);
      this.provinces = [];
    }
  }

  async loadHotAddress() {
    console.log('Loading hot addresses...');
    try {
      const request: PagingRequest = {
        Keyword: '',
        PageIndex: 1,
        PageSize: this._quantity,
        TypeScreen: this._type
      };
      const res = await this.addressService.getPagingClient(request).toPromise();
      if (res && res.resultObj) {
        this.dataAddress = res.resultObj;
        console.log('Hot addresses loaded:', this.dataAddress.items?.length || 0);
      }
    } catch (error) {
      console.error('Error loading hot address:', error);
      this.dataAddress = { items: [] };
    }
  }

  slideConfig = {
    infinite: true,
    slidesToShow: 4,
    autoplay: false,
    autoplaySpeed: 3000,
    nextArrow:
      `<button class="slick-button slick-button-left">
      <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
      </svg>
    </button>`,
    prevArrow:
      `<button class="slick-button slick-button-right">
      <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m15 19-7-7 7-7"/>
      </svg>
    </button>`,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 3,
        },
      },
    ],
  };
}
