.pnr-section .card {
    border: none;
}

.pnr-section .card-header {
    border-bottom: none;
}

.pnr-box {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.pnr-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pnr-code {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: bold;
    letter-spacing: 2px;
    color: #0d6efd;
    background: linear-gradient(45deg, #0d6efd, #0dcaf0);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.badge {
    font-size: 0.9rem;
    padding: 0.5em 1em;
}

/* ... existing styles ... */

/* History Section Styles */
.history-item {
    transition: all 0.2s ease;
}

.history-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.history-action-icon {
    transition: transform 0.2s ease;
}

.history-item:hover .history-action-icon {
    transform: scale(1.1);
}

.status-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    border: 1px solid;
    transition: all 0.2s ease;
}

.status-badge.old-status {
    background-color: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.status-badge.new-status {
    background-color: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
}

.history-timeline {
    position: relative;
}

.history-timeline::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e5e7eb, transparent);
}

/* Tab Styles */
.tab-button {
    transition: all 0.3s ease;
    position: relative;
}

.tab-button:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

.tab-button.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.tab-content {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rotate-180 {
    transform: rotate(180deg);
}

.translate-y-2 {
    transform: translateY(0.5rem);
}

.translate-y-0 {
    transform: translateY(0);
}