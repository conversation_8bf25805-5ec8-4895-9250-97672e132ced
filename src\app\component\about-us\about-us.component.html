<div class="relative bg-[url('/assets/img/background/about-us-bg.jpg')] pb-36 bg-cover bg-no-repeat bg-custom">
    <div class="max-w-7xl m-auto pt-28 relative max-md:px-4">
        <div class="max-w-[1200px] m-auto relative z-10">
            <div class="md:p-14 p-4 border-primary-600 border-[3px] rounded-lg backdrop-blur-md">
                <div class="md:grid md:grid-cols-12 gap-8">
                    <div class="col-span-7">
                        <span
                            class="w-full py-4 text-3xl leading-none text-h font-extrabold uppercase bg-gradient-to-r hover:bg-gradient-to-l bg-[#f3ec78] bg-gradient-310 from-[#fb6340] via-[#fb6340] to-[#df9523] bg-clip-text text-transparent">
                            <span class="text-[#00003b]">
                                {{translatePage?.['S0004-0002_0002'] }}
                            </span>
                            <span>
                                {{translatePage?.['S0004-0002_0055'] }}
                            </span>
                        </span>
                        <div class="text-justify pt-4 text-[#00003b]">
                            <p class="mb-2 ">{{translatePage?.['S0004-0002_0003'] }}
                                <strong>{{translatePage?.['S0004-0002_0094'] }}</strong>
                                <span class="text-primary-600 font-extrabold">
                                    ({{translatePage?.['S0004-0002_0055'] }})
                                </span>
                                {{translatePage?.['S0004-0002_0084'] }}
                            </p>
                            <p class="mb-2">
                                {{translatePage?.['S0004-0002_0004'] }} </p>
                            <p> {{translatePage?.['S0004-0002_0005'] }} </p>
                        </div>
                    </div>
                    <div class="col-span-5 max-md:mt-8">
                        <div class="rounded-lg  border-gray-500 h-fit relative">
                            <i class="absolute bg-gray-400 w-full h-full rounded-lg z-10 rotate-2 shadow-lg"></i>
                            <img src="/assets/img/background/about_1.jpg"
                                class="object-cover w-full object-[60%_73%] aspect-[16/9] rounded-lg  relative z-10  shadow-lg">
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="bg-white min-h-screen pb-12 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <div class="mt-4 flex flex-col items-center justify-center ">
            <h1
                class="text-center md:text-4xl text-2xl flex flex-col gap-2 py-2 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                {{translatePage?.['S0004-0002_0006'] }}
            </h1>
            <div class="max-w-5xl mt-8 max-md:text-sm">
                <p class="text-center mb-4">
                    <span>
                        {{translatePage?.['S0004-0002_0007'] }}
                        <strong class="text-primary-600">{{translatePage?.['S0004-0002_0055'] }}</strong>
                        {{translatePage?.['S0004-0002_0095'] }}
                    </span>
                </p>
                <p class="text-center mb-4">
                    {{translatePage?.['S0004-0002_0008'] }}
                </p>
                <p class="text-center">
                    {{translatePage?.['S0004-0002_0009'] }}
                </p>
            </div>
        </div>

        <div class="mt-16 flex flex-col items-center justify-center ">
            <div class="contents">
                <section class="">
                    <div class="container mx-auto px-4">
                        <div class="max-w-4xl mx-auto p-4">
                            <h1
                                class="text-center md:text-4xl text-2xl flex flex-col gap-2 py-2 mb-12 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                                {{translatePage?.['S0004-0002_0010'] }}
                            </h1>
                            <!-- Timeline bar -->
                            <div class="relative mb-16">
                                <!-- Timeline main line -->
                                <div class="absolute h-1 w-full bg-gray-200 top-1/2 transform -translate-y-1/2"></div>
                                <div class="absolute h-1 bg-primary-500 top-1/2 transform -translate-y-1/2 transition-all duration-500"
                                    [style.width.%]="getTimelineFillWidth()"></div>
                                <div class="flex justify-between relative">

                                    <!-- Left extension -->
                                    <div class="w-8 h-1 bg-primary-500 absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2"
                                        [style.width.%]="10"></div>

                                    <!-- Timeline items -->
                                    <div *ngFor="let item of timelineData; let index = index"
                                        class="flex flex-col items-center relative">
                                        <button
                                            class="w-6 h-6 rounded-full focus:outline-none z-10 transform transition-transform relative"
                                            [ngClass]="{'bg-primary-500': selectedItem?.id === item.id}"
                                            [class.bg-white]="selectedItem?.id !== item.id"
                                            style="border: 2px solid #CBD5E0" (click)="selectItem(item)">
                                            <span class="sr-only">{{ item.year }}</span>
                                        </button>
                                        <span *ngIf="selectedItem?.id === item.id"
                                            class="absolute inset-0 w-full h-full rounded-full border-2 border-primary-500 animate-ping"></span>
                                        <span class="text-base font-medium absolute whitespace-nowrap" [ngClass]="{
                                      'bottom-full mb-2': index % 2 === 0,
                                      'top-full mt-2': index % 2 !== 0
                                    }">
                                            {{ item.year }}
                                        </span>
                                    </div>

                                    <!-- Right extension -->
                                    <div [style.width.%]="10"
                                        class="w-8 h-1 bg-gray-200 absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2">
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="selectedItem"
                                class="bg-white md:p-6 p-2 rounded-lg shadow-md md:min-w-[48rem] flex md:gap-6 gap-2 border"
                                [@fadeSlide]="'in'">
                                <h3
                                    class="font-semibold mb-2 border-r-4 border-primary-600 pr-6 max-w-36 text-center md:text-4xl text-2xl flex-col gap-2 py-2 leading-none bg-primary-500 bg-clip-text text-transparent flex items-center justify-center">
                                    {{ selectedItem.year }}</h3>
                                <p [innerHTML]="selectedItem.content" class=" max-md:text-sm"></p>
                            </div>
                        </div>



                    </div>
                </section>
            </div>
        </div>



        <div class="mt-10 md:mt-16 flex flex-col items-center justify-center ">
            <div class="w-full px-4">
                <div class="flex justify-center">
                    <h1
                        class="text-center md:text-4xl text-2xl flex flex-col gap-2 py-2 mb-8 max-sm:mb-2 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                        {{translatePage?.['S0004-0002_0017'] }}
                    </h1>
                </div>
                <div class="relative max-sm:px-4">
                    <ngx-slick-carousel #slickModal3="slick-carousel" [config]="slideConfig2"
                        class="grid md:grid-cols-4 grid-cols-2 md:gap-8 gap-8  py-10 max-md:pt-0 max-sm:py-0">
                        <div ngxSlickItem *ngFor="let box of visibleItems">
                            <div
                                class="h-full flex px-4  flex-col max-sm:flex-row max-sm:gap-2 justify-center items-center bg-white overflow-hidden">
                                <div
                                    class="md:text-4xl text-2xl mb-4 flex items-center justify-center border-2 border-primary-600 rounded-full md:w-40 md:h-40 w-24 h-24  bg-primary-50">
                                    <h1 class="text-center text-7xl w-full">{{ box.emoji }}</h1>
                                </div>
                                <div class="flex flex-col max-sm:flex-col  items-center justify-center">
                                    <h3
                                        class="text-xl max-sm:text-base font-bold text-gray-800 mb-2 py-2 max-sm:py-0 text-center max-sm:text-left w-full">
                                        {{ translatePage?.[box.title] }}
                                    </h3>
                                    <div
                                        class="flex flex-col max-sm:flex-col items-center justify-center max-sm:items-start">
                                        <p
                                            class="text-gray-600 mb-4 text-sm text-center max-sm:text-left max-sm:text-xs">
                                            {{ translatePage?.[box.description] }}
                                        </p>
                                        <button [routerLink]="box.link"
                                            class="text-primary-600 font-semibold text-center max-sm:text-left flex items-center transition-colors duration-300 hover:text-primary-800">
                                            {{ translatePage?.['S0004-0002_0062'] }}
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-arrow-right ml-2 h-4 w-4">
                                                <path d="M5 12h14"></path>
                                                <path d="m12 5 7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ngx-slick-carousel>
                    <div class="flex gap-4 justify-center max-sm:pt-10">
                        <div class="md:absolute top-1/2 transform bg-white -translate-y-1/2 left-0 w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center cursor-pointer group hover:border-primary-600"
                            (click)="prev3()">
                            <svg class="w-8 h-8 fill-gray-800 group-hover:fill-primary-600 cursor-pointer"
                                viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z">
                                </path>
                            </svg>
                        </div>

                        <div class="md:absolute top-1/2 transform bg-white -translate-y-1/2 right-0 w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center cursor-pointer group hover:border-primary-600"
                            (click)="next3()">
                            <svg class="w-8 h-8 fill-gray-800 group-hover:fill-primary-600 cursor-pointer"
                                viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-16 ">
            <div class="max-w-7xl mx-auto px-4 ">
                <div class="flex justify-center">
                    <h1
                        class="text-center md:text-4xl text-2xl flex flex-col gap-2 py-2 mb-8 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                        {{translatePage?.['S0004-0002_0027'] }}
                    </h1>
                </div>
                <div class="grid md:grid-cols-2 grid-cols-1 md:gap-x-8 gap-4">
                    <div class=" p-2 sm:p-4 aos-init aos-animate" data-aos="zoom-in" data-aos-delay="0">
                        <div class=" hover:scale-105 transition-transform duration-300 ease-in-out">
                            <div class="flex gap-4">
                                <div class="hexagon  min-w-24 w-24 h-24 bg-primary-600"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-tag sm:w-8 sm:h-8 text-white">
                                        <path
                                            d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z">
                                        </path>
                                        <circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle>
                                    </svg>
                                </div>
                                <div class="hexagon-text">
                                    <h3 class="text-base md:text-2xl font-semibold mb-1 sm:mb-2 ">
                                        {{translatePage?.['S0004-0002_0028'] }}
                                    </h3>
                                    <p class="text-black text-[10px] md:text-sm">
                                        {{translatePage?.['S0004-0002_0029'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" p-2 sm:p-4 aos-init aos-animate" data-aos="zoom-in" data-aos-delay="0">
                        <div class=" hover:scale-105 transition-transform duration-300 ease-in-out">
                            <div class="flex gap-4">
                                <div class="hexagon  min-w-24 w-24 h-24 bg-primary-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-tag sm:w-8 sm:h-8 text-white">
                                        <path
                                            d="M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z">
                                        </path>
                                        <path d="M21 16v2a4 4 0 0 1-4 4h-5"></path>
                                    </svg>
                                </div>
                                <div class="hexagon-text">
                                    <h3 class="text-base md:text-2xl font-semibold mb-1 sm:mb-2 ">
                                        {{translatePage?.['S0004-0002_0030'] }}
                                    </h3>
                                    <p class="text-black text-[10px] md:text-sm">
                                        {{translatePage?.['S0004-0002_0031'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class=" p-2 sm:p-4 aos-init aos-animate" data-aos="zoom-in" data-aos-delay="200">
                        <div class=" hover:scale-105 transition-transform duration-300 ease-in-out">
                            <div class="flex gap-4">
                                <div class="hexagon  min-w-24 w-24 h-24 bg-primary-600"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-briefcase w-6 h-6 sm:w-8 sm:h-8 text-white">
                                        <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                                        <rect width="20" height="14" x="2" y="6" rx="2"></rect>
                                    </svg></div>
                                <div class="hexagon-text">
                                    <h3 class="text-base md:text-2xl font-semibold mb-1 sm:mb-2 text-black">
                                        {{translatePage?.['S0004-0002_0032'] }}
                                    </h3>
                                    <p class="text-black text-[10px] md:text-sm">
                                        {{translatePage?.['S0004-0002_0033'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" p-2 sm:p-4 aos-init aos-animate" data-aos="zoom-in" data-aos-delay="300">
                        <div class=" hover:scale-105 transition-transform duration-300 ease-in-out">
                            <div class="flex gap-4">
                                <div class="hexagon  min-w-24 w-24 h-24 bg-primary-600"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-star w-6 h-6 sm:w-8 sm:h-8 text-white">
                                        <polygon
                                            points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2">
                                        </polygon>
                                    </svg></div>
                                <div class="hexagon-text">
                                    <h3 class="text-base md:text-2xl font-semibold mb-1 sm:mb-2 text-black">
                                        {{translatePage?.['S0004-0002_0034'] }}
                                    </h3>
                                    <p class="text-black text-[10px] md:text-sm">
                                        {{translatePage?.['S0004-0002_0035'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" p-2 sm:p-4 aos-init aos-animate" data-aos="zoom-in" data-aos-delay="400">
                        <div class=" hover:scale-105 transition-transform duration-300 ease-in-out">
                            <div class="flex gap-4">
                                <div class="hexagon  min-w-24 w-24 h-24 bg-primary-600"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-network w-6 h-6 sm:w-8 sm:h-8 text-white">
                                        <rect x="16" y="16" width="6" height="6" rx="1"></rect>
                                        <rect x="2" y="16" width="6" height="6" rx="1"></rect>
                                        <rect x="9" y="2" width="6" height="6" rx="1"></rect>
                                        <path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"></path>
                                        <path d="M12 12V8"></path>
                                    </svg></div>
                                <div class="hexagon-text">
                                    <h3 class="text-base md:text-2xl font-semibold mb-1 sm:mb-2 text-black">
                                        {{translatePage?.['S0004-0002_0036'] }}
                                    </h3>
                                    <p class="text-black text-[10px] md:text-sm">
                                        {{translatePage?.['S0004-0002_0037'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" p-2 sm:p-4 aos-init aos-animate" data-aos="zoom-in" data-aos-delay="500">
                        <div class=" hover:scale-105 transition-transform duration-300 ease-in-out">
                            <div class="flex gap-4">
                                <div class="hexagon min-w-24 w-24 h-24 bg-primary-600"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-pen-tool w-6 h-6 sm:w-8 sm:h-8 text-white">
                                        <path
                                            d="M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z">
                                        </path>
                                        <path
                                            d="m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18">
                                        </path>
                                        <path d="m2.3 2.3 7.286 7.286"></path>
                                        <circle cx="11" cy="11" r="2"></circle>
                                    </svg></div>
                                <div class="hexagon-text">
                                    <h3 class="text-base md:text-2xl font-semibold mb-1 sm:mb-2 text-black">
                                        {{translatePage?.['S0004-0002_0039'] }}
                                    </h3>
                                    <p class="text-black text-[10px] md:text-sm">
                                        {{translatePage?.['S0004-0002_0038'] }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-16">
            <div class="flex justify-center">
                <!-- <span
                    class="md:text-4xl text-3xl text-center mb-12  leading-none text-h font-extrabold uppercase bg-gradient-to-r hover:bg-gradient-to-l bg-[#f3ec78] bg-gradient-310 from-[#fb6340c9] via-[#fb6340c9] to-[#fbb140] bg-clip-text text-transparent">
                    {{translatePage?.['S0004-0002_0042'] }}
                </span> -->
                <h1
                    class="text-center md:text-4xl text-2xl flex flex-col gap-2 py-2 mb-8 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                    {{translatePage?.['S0004-0002_0042'] }}
                </h1>
            </div>
            <div class="md:py-24 flex flex-col items-center justify-center">
                <div class="max-w-7xl w-full">
                    <div class="relative  flex flex-col items-center justify-center">
                        <div
                            class="relative md:w-56 md:h-56 inset-0 flex items-center justify-center max-md:flex-col max-md:w-full">
                            <div
                                class="hover:scale-105 cursor-pointer hover:animate-spin relative w-56 h-56 bg-transparent border-dashed border-4 border-primary-600 rounded-full flex items-center justify-center text-white text-lg md:text-2xl font-bold shadow-lg">
                                <h1 class="text-center text-primary-600 text-4xl font-extrabold">
                                    300+<br>
                                    {{translatePage?.['S0004-0002_0064'] }}
                                </h1>
                            </div>
                            <div
                                class="max-md:w-full max-md:flex max-md:flex-col max-md:gap-4 max-md:mt-4 max-md:overflow-auto">
                                <!-- tài chính -->
                                <div class="hover:scale-105 cursor-pointer md:absolute right-[22.5%] -top-[25%]">
                                    <div
                                        class="md:absolute z-20 transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md  overflow-visible">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-trending-up w-8 h-8">
                                                <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                                <polyline points="16 7 22 7 22 13"></polyline>
                                            </svg><span class="text-xs md:text-sm">
                                                {{translatePage?.['S0004-0002_0043'] }}
                                            </span>
                                        </div>
                                        <div class="flex flex-row absolute md:left-24 left-20 overflow-visible">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/VCB.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-3 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/13] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/acb.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-3 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/mb.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-3 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/12] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/ocb.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[20/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/maybank.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute -right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- xây dựng -->
                                <div class="md:absolute -right-[25%] top-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-building w-8 h-8">
                                                <rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect>
                                                <path d="M9 22v-4h6v4"></path>
                                                <path d="M8 6h.01"></path>
                                                <path d="M16 6h.01"></path>
                                                <path d="M12 6h.01"></path>
                                                <path d="M12 10h.01"></path>
                                                <path d="M12 14h.01"></path>
                                                <path d="M16 10h.01"></path>
                                                <path d="M16 14h.01"></path>
                                                <path d="M8 10h.01"></path>
                                                <path d="M8 14h.01"></path>
                                            </svg><span class="text-xs md:text-sm">
                                                {{translatePage?.['S0004-0002_0044'] }}
                                            </span>
                                        </div>
                                        <div class="flex flex-row absolute md:left-24 left-20 overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/sonkim.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/avery_dennison.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[20/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/vivablast_v2.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[11/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/viglacera.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <!-- <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[10/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1" src="/assets/img/partner/nho.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div> -->
                                                <div
                                                    class="absolute -right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- giáo dục -->
                                <div class="md:absolute -right-[25%] bottom-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-graduation-cap w-8 h-8">
                                                <path
                                                    d="M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z">
                                                </path>
                                                <path d="M22 10v6"></path>
                                                <path d="M6 12.5V16a6 3 0 0 0 12 0v-3.5"></path>
                                            </svg><span class="text-xs md:text-sm">
                                                {{translatePage?.['S0004-0002_0046'] }}
                                            </span>
                                        </div>
                                        <div class="flex flex-row absolute md:left-24 left-20 overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[12/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/ila.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/6] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/fpt.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/13] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/yola.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/rmit.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <!-- <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/Onschool.png"
                                                            style="color: transparent;">
                                                    </div>
                                                </div> -->
                                                <div
                                                    class="absolute -right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="md:absolute right-[22.5%] -bottom-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-heart w-8 h-8">
                                                <path
                                                    d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
                                                </path>
                                            </svg><span class="text-xs md:text-sm">
                                                {{translatePage?.['S0004-0002_0048'] }}
                                            </span>
                                        </div>
                                        <div class="flex flex-row absolute md:left-24 left-20 overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[12/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/tn.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[12/7] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/tofflon.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/7] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/yviet.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/6] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/jw_euvipharm.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[11/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/vina_media.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute -right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="md:absolute left-[22.5%] -bottom-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-shopping-bag w-8 h-8">
                                                <path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"></path>
                                                <path d="M3 6h18"></path>
                                                <path d="M16 10a4 4 0 0 1-8 0"></path>
                                            </svg><span class="text-xs md:text-[12.5px]">
                                                {{translatePage?.['S0004-0002_0050'] }}
                                            </span>
                                        </div>

                                        <div
                                            class="flex flex-row absolute md:right-24 max-md:left-24  overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/andros.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-3 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/camel.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[9/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/hino.png"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-2 min-w-12">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[9/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/beer_vana.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-3 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[9/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/tapack.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/11] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/ab.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute md:-left-5 max-md:-right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="md:absolute -left-[25%] bottom-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-briefcase w-8 h-8">
                                                <path d="M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                                                <rect width="20" height="14" x="2" y="6" rx="2"></rect>
                                            </svg><span class="text-xs md:text-sm">
                                                {{translatePage?.['S0004-0002_0049'] }}
                                            </span>
                                        </div>

                                        <div
                                            class="flex flex-row absolute md:right-24 max-md:left-24 right-20 overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/12] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/concentrix.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/boxme.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[10/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/evn.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/11] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/pvoil.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/11] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/aqua.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <!-- <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[11/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/savvycom.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div> -->
                                                <div
                                                    class="absolute md:-left-5 max-md:-right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="md:absolute -left-[25%] top-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-tv w-8 h-8">
                                                <rect width="20" height="15" x="2" y="7" rx="2" ry="2"></rect>
                                                <polyline points="17 2 12 7 7 2"></polyline>
                                            </svg><span class="text-xs md:text-[12.5px]">
                                                {{translatePage?.['S0004-0002_0047'] }}
                                            </span>
                                        </div>

                                        <div
                                            class="flex flex-row absolute md:right-24 max-md:left-24 right-20 overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="Logo 1 của Thời trang" loading="lazy"
                                                            class="w-auto aspect-[14/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/tuoitre.webp"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[9/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/vba.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[13/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/saigon_heat.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[8/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/mab.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/bpro.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute md:-left-5 max-md:-right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="md:absolute left-[22.5%] -top-[25%]">
                                    <div
                                        class="hover:scale-105 cursor-pointer md:absolute transform md:-translate-x-1/2 md:-translate-y-1/2 flex items-center">
                                        <div
                                            class="relative w-24 z-20 md:w-28 md:h-24 bg-primary-600 clip-hexagon flex flex-col items-center justify-center text-white font-semibold text-center p-2 shadow-md ">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-shirt w-8 h-8">
                                                <path
                                                    d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z">
                                                </path>
                                            </svg><span class="text-xs md:text-sm">
                                                {{translatePage?.['S0004-0002_0045'] }}
                                            </span>
                                        </div>

                                        <div
                                            class="flex flex-row absolute md:right-24 max-md:left-24 right-20 overflow-visible ">
                                            <div
                                                class="relative border-2 border-orange-300 border-dashed rounded-full py-2 flex h-16 w-auto px-6">
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-20">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/logo-converse-02.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-20">

                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/vera.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 ">
                                                    <div class=" w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[15/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/map.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-3 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/skechers.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div class="bg-white rounded-sm overflow-hidden  mx-1 min-w-12">
                                                    <div class="min-w-12 w-auto h-full">
                                                        <img alt="" loading="lazy"
                                                            class="w-auto aspect-[16/9] max-w-none h-full object-contain"
                                                            decoding="async" data-nimg="1"
                                                            src="/assets/img/partner/jockey.jpg"
                                                            style="color: transparent;">
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute md:-left-5 max-md:-right-5 px-2 bg-primary-600 rounded-full overflow-hidden shadow-sm mx-1 transform translate-y-1/2">
                                                    <div class="w-full h-full flex justify-center items-center">
                                                        <svg class="w-6 h-6 text-white font-extrabold"
                                                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                            width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round"
                                                                stroke-linejoin="round" stroke-width="2"
                                                                d="M5 12h14m-7 7V5" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>



    </div>
</div>
<!-- airline partner -->
<div class="mt-10 border-t md:py-16 py-4">
    <div class="md:grid grid-cols-11 gap-8  md:w-[90%] m-auto max-md:px-4">
        <div class="col-span-3">
            <h3 class="text-2xl font-medium mb-3 inline-block"> {{translatePage?.['S0004-0002_0066'] }}
            </h3>
            <p class="text-base mb-4 text-justify"> {{translatePage?.['S0004-0002_0067'] }} </p>
        </div>
        <div class="col-span-8 ">
            <ul ngbNav #nav1="ngbNav" [(activeId)]="active" class="border-b-2 border-primary-600   flex flex-row">
                <li [ngbNavItem]="1"
                    [ngClass]="{'border border-primary-600 rounded-tl-md rounded-tr-md -mb-[1px] border-b-0 bg-primary-600 text-white': active === 1}"
                    class="py-2 px-4  text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-t-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <button ngbNavLink>{{translatePage?.['S0004-0002_0068'] }}</button>
                    <ng-template ngbNavContent>
                        <div [ngClass]="{'hidden': active !== 1}" class="grid md:grid-cols-5 grid-cols-2 gap-4">
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width" loading="lazy"
                                        src="assets/img/partner/airline/VNA.png" alt="VietnamAirline"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2" loading="lazy"
                                        src="assets/img/partner/airline/VJ.png" alt="VietJet"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width" loading="lazy"
                                        src="assets/img/partner/airline/Bamboo.png" alt="Bamboo"></div>
                            </div>

                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width" loading="lazy"
                                        src="assets/img/partner/airline/Pacific.png" alt="Pacific"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width" loading="lazy"
                                        src="assets/img/partner/airline/VietravelAirlines.png" alt="AmericanAirlines">
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="2"
                    [ngClass]="{'border border-primary-600 rounded-tl-md rounded-tr-md -mb-[1px] border-b-0 bg-primary-600 text-white': active === 2}"
                    class="py-2 px-4 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-t-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <button ngbNavLink>{{translatePage?.['S0004-0002_0069'] }}</button>
                    <ng-template ngbNavContent>
                        <div [ngClass]="{'hidden': active !== 2}"
                            class="grid md:grid-cols-5 grid-cols-2 gap-x-4 gap-y-2">
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/TK.jpg" alt="TK"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/KoreanAir.png" alt="KoreanAir"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/PhilippineAirlines.png"
                                        alt="PhilippineAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/EVA.png" alt="EVA"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[22/9] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/Emirates.png" alt="Emirates"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/qatarairways.png" alt="qatarairways"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/ANA.png" alt="ANA">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/Starlux.png" alt="Starlux"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/TwayAir.png" alt="TwayAir"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/CathayPacific.png" alt="CathayPacific"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/ChinaSouthernAirlines.png"
                                        alt="ChinaSouthernAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/Delta.png" alt="Delta"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/AirFrance.png" alt="AirFrance"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/AirAsia.png" alt="AirAsia"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/AA.png" alt="AA"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/SingaporeAirlines.png" alt="SingaporeAirlines">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/ThaiAirways.png" alt="ThaiAirways"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[22/9] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/JapanAirlines.png" alt="JapanAirlines"></div>
                            </div>

                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center "
                                        loading="lazy" src="assets/img/partner/airline/ChinaEasternAirlines.png"
                                        alt="ChinaEasternAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/ChinaAirlines.png" alt="ChinaAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[22/9] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/Scootlogo.png" alt="Scoot"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/AsianaAirlines.png" alt="AsianaAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/Shenzhen_Airlines_logo.png"
                                        alt="AsianaAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/Air_Canada_logo.png" alt="AsianaAirlines"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img
                                        class="full-width aspect-[16/6] p-3 object-contain object-center" loading="lazy"
                                        src="assets/img/partner/airline/AirChina.png" alt="AirChina"></div>
                            </div>
                        </div>
                    </ng-template>
                </li>
            </ul>

            <div [ngbNavOutlet]="nav1" class="mt-2"></div>

        </div>
    </div>
</div>

<!-- hotel partner -->
<div class="border-t  md:pt-16 py-4">
    <div class="md:grid grid-cols-11 gap-8  md:w-[90%] m-auto max-md:px-4">
        <div class="col-span-3 ">
            <h3 class="text-2xl font-medium mb-3">{{translatePage?.['S0004-0002_0070'] }}</h3>
            <p class="text-base mb-4 text-justify"> {{translatePage?.['S0004-0002_0071'] }}</p>
        </div>
        <div class="col-span-8">
            <ul ngbNav #nav2="ngbNav" [(activeId)]="activeHotel" class="border-b-2 border-primary-600   flex flex-row">
                <li [ngbNavItem]="1"
                    [ngClass]="{'border border-primary-600 rounded-tl-md rounded-tr-md -mb-[1px] border-b-0 bg-primary-600 text-white': activeHotel === 1}"
                    class="py-2 px-4  text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-t-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <button ngbNavLink>{{translatePage?.['S0004-0002_0068'] }}</button>
                    <ng-template ngbNavContent>
                        <div [ngClass]="{'hidden': activeHotel !== 1}" class="grid md:grid-cols-5 grid-cols-2 gap-4">
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/Vinpearl.png" alt="Vinpearl"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/FLC.png" alt="FLC"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/NEW WORLD.png" alt="FLC"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/FURAMA.png" alt="FLC"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/liberty.jpg" alt="FLC"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/MuongThanh.png" alt="MuongThanh"></div>
                            </div>


                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/Fusion.png" alt="Fusion">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width px-5 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/Havana.png" alt="Fusion">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width aspect-[15/8]" loading="lazy"
                                        src="assets/img/partner/hotel/Sheraton logo.png" alt="Fusion">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width aspect-[16/7]" loading="lazy"
                                        src="assets/img/partner/hotel/ANG_logo.png" alt="Fusion">
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </li>
                <li [ngbNavItem]="2"
                    [ngClass]="{'border border-primary-600 rounded-tl-md rounded-tr-md -mb-[1px] border-b-0 bg-primary-600 text-white': activeHotel === 2}"
                    class="py-2 px-4 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-t-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <button ngbNavLink>{{translatePage?.['S0004-0002_0069'] }}</button>
                    <ng-template ngbNavContent>
                        <div [ngClass]="{'hidden': activeHotel !== 2}"
                            class="grid md:grid-cols-5 grid-cols-2 gap-x-4 gap-y-2">
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/Marriott.png" alt="Marriott"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 aspect-[16/9]" loading="lazy"
                                        src="assets/img/partner/hotel/Accor.png" alt="Accor"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width  py-3" loading="lazy"
                                        src="assets/img/partner/hotel/wyndham hotels.png" alt="wyndham hotels"></div>
                            </div>

                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width py-3" loading="lazy"
                                        src="assets/img/partner/hotel/Best_Western_Hotels_&_Resorts_.png"
                                        alt="Best_Western_Hotels_&_Resorts_"></div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width py-3" loading="lazy"
                                        src="assets/img/partner/hotel/Hyatt_Logo.png" alt="Hyatt_Logo">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 py-3" loading="lazy"
                                        src="assets/img/partner/hotel/Starwood_Hotels_and_Resorts_logo.png"
                                        alt="Starwood_Hotels_and_Resorts_logo">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 py-3" loading="lazy"
                                        src="assets/img/partner/hotel/Plateno_LOGO.png" alt="Plateno_LOGO">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 py-3" loading="lazy"
                                        src="assets/img/partner/hotel/choice-hotels-international-vector-logo-2022.png"
                                        alt="choice-hotels-international-vector-logo-2022">
                                </div>
                            </div>
                            <div class="col-6 col-md-3 ng-star-inserted">
                                <div class="partner-item"><img class="full-width p-2 px-3" loading="lazy"
                                        src="assets/img/partner/hotel/IHG_logo_InterContinental_Hotels_Group.png"
                                        alt="IHG_logo_InterContinental_Hotels_Group">
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </li>

            </ul>

            <div [ngbNavOutlet]="nav2" class="mt-2"></div>

        </div>
    </div>
</div>

<div class="min-h-72 md:mt-16 mt-8">
    <div>
        <div class="h-[1px] bg-black/10 relative w-full">
            <h3
                class="hidden md:block bg-white  text-[#333] text-2xl font-bold mb-10 px-4 py-1 absolute right-1/2 text-center transform translate-x-1/2 -translate-y-1/2">
                {{translatePage?.['S0004-0002_0082']}}</h3>
        </div>
        <div class="max-w-7xl m-auto p-4">
            <h1
                class="md:hidden text-left md:text-4xl text-2xl flex flex-col gap-2 py-2 mb-8 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                {{translatePage?.['S0004-0002_0082']}}
            </h1>
            <div class="relative">
                <ngx-slick-carousel #slickModal2="slick-carousel" [config]="slideConfig"
                    class="grid md:grid-cols-4 grid-cols-2 md:gap-8 gap-4 py-10 max-md:pt-0">
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/CTCP.jpg" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">

                                    {{translatePage?.['S0004-0002_0072']}}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/Gold Agent  Vietnam Airlines.jpg" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">

                                    {{translatePage?.['S0004-0002_0073']}}</p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/Agent  Bamboo Airways.jpg" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">


                                    {{translatePage?.['S0004-0002_0074']}}</p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/TOP AGENT AWARD Philippine Airlines.jpg" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">


                                    {{translatePage?.['S0004-0002_0075']}}</p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/Agent Vietjet Air.jpg" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">

                                    {{translatePage?.['S0004-0002_0076']}}</p>
                            </div>
                        </div>
                    </div>

                </ngx-slick-carousel>
                <div class="flex gap-4 justify-center">
                    <div class="md:absolute top-1/2 transform bg-white -translate-y-1/2 left-0 w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center cursor-pointer group hover:border-primary-600"
                        (click)="prev2()">
                        <svg class="w-8 h-8 fill-gray-800 group-hover:fill-primary-600 cursor-pointer"
                            viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z">
                            </path>
                        </svg>
                    </div>

                    <div class="md:absolute top-1/2 transform bg-white -translate-y-1/2 right-0 w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center cursor-pointer group hover:border-primary-600"
                        (click)="next2()">
                        <svg class="w-8 h-8 fill-gray-800 group-hover:fill-primary-600 cursor-pointer"
                            viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="min-h-72 md:mb-16 mb-8">
    <div>
        <div class="h-[1px] bg-black/10 relative w-full">
            <h3
                class="hidden md:block bg-white  text-[#333] text-2xl font-bold mb-10 px-4 py-1 absolute right-1/2 text-center transform translate-x-1/2 -translate-y-1/2">
                {{translatePage?.['S0004-0002_0083']}}</h3>
        </div>
        <div class="max-w-7xl m-auto pt-4">
            <!-- <h3 class="text-3xl font-medium mb-3 md:hidden block"> Giải thưởng nổi bật </h3> -->
            <h1
                class="md:hidden text-left md:text-4xl text-2xl flex flex-col gap-2 py-2 mb-8 leading-none font-extrabold bg-[#00003b]  bg-clip-text text-transparent">
                {{translatePage?.['S0004-0002_0083']}}
            </h1>

            <div class="relative">
                <ngx-slick-carousel #slickModal="slick-carousel" [config]="slideConfig"
                    class="grid md:grid-cols-4 grid-cols-2 md:gap-8 gap-4 md:pt-10 max-md:py-10 max-md:pt-0">
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/RUBY REWARD 2023.png" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">


                                    {{translatePage?.['S0004-0002_0077']}}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/Excellent Agent of The Year 2024.png" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">

                                    {{translatePage?.['S0004-0002_0078']}}</p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/vietravel_airlines.png" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">


                                    {{translatePage?.['S0004-0002_0079']}}</p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/vietjet.png" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">

                                    {{translatePage?.['S0004-0002_0080']}}</p>
                            </div>
                        </div>
                    </div>
                    <div ngxSlickItem class="md:p-4 p-2">
                        <div class="rounded-lg shadow-md overflow-hidden border border-gray-200 bg-white py-4">
                            <div class="relative h-32 ">
                                <img class="w-full h-full object-contain hover:scale-105"
                                    src="/assets/img/prize/Vietnam_airines.png" alt="Giải thưởng 1">

                            </div>
                            <div class="md:px-4 md:pt-4  px-2 pt-2 text-center flex justify-center ">
                                <p class="text-[#5e656b] text-xs h-12 font-bold uppercase  line-clamp-3">

                                    {{translatePage?.['S0004-0002_0081']}}</p>
                            </div>
                        </div>
                    </div>

                </ngx-slick-carousel>
                <div class="flex gap-4 justify-center">
                    <div class="md:absolute top-1/2 transform bg-white -translate-y-1/2 left-0 w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center cursor-pointer group hover:border-primary-600"
                        (click)="prev()">
                        <svg class="w-8 h-8 fill-gray-800 group-hover:fill-primary-600 cursor-pointer"
                            viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z">
                            </path>
                        </svg>
                    </div>

                    <div class="md:absolute top-1/2 transform bg-white -translate-y-1/2 right-0 w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center cursor-pointer group hover:border-primary-600"
                        (click)="next()">
                        <svg class="w-8 h-8 fill-gray-800 group-hover:fill-primary-600 cursor-pointer"
                            viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-email-contact></app-email-contact>
<div>
    <div>
        <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3918.951917707127!2d106.67065470983857!3d10.81499165843828!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3175291f005f1fe3%3A0x11f408fb97ddb726!2zNzYgxJAuIELhuqFjaCDEkOG6sW5nLCBQaMaw4budbmcgMiwgVMOibiBCw6xuaCwgSOG7kyBDaMOtIE1pbmgsIFZp4buHdCBOYW0!5e0!3m2!1svi!2s!4v1720496451772!5m2!1svi!2s"
            width="100%" height="350" class="border-none rounded-lg shadow-md" allowfullscreen="" loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"></iframe>
    </div>
</div>