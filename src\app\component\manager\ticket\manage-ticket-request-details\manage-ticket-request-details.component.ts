import { Component, inject, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule} from '@angular/forms';
import { FlightService } from '../../../../service/flight/flight.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';
import { CommonModule, Location } from '@angular/common';
import { lastValueFrom } from 'rxjs';
import { WorldService } from '../../../../service/world/world.service';
import { formatDateTo_ddMMyyyy } from '../../../../service/common/function.common';
import { NumberFormatPipe } from '../../../../pipe/number-format/number-format.pipe';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';


@Component({
  selector: 'app-manage-ticket-request-details',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NumberFormatPipe

  ],
  templateUrl: './manage-ticket-request-details.component.html',
  styleUrl: './manage-ticket-request-details.component.css'
})

export class ManageTicketRequestDetailsComponent {
  orderAvailable: any;
  orderDetails: any;
  inforAirports: any[] = [];
  servicePrice: number = 0;
  state = 200;
  @ViewChild('modalNotification') modalNotification: any;
  @ViewChild('modalNote') modalNote: any;
  // Modal properties
  titleModal: string = '';
  contentModal: string = '';

  // Update PNR property
  pnrList: any[] = [];

  // New note properties
  noteUserList: any[] = [];
  newNoteContent: string = '';

  editNoteIndex: number | null = null;
  editNoteContent: string = '';

  showAllNotes: boolean = false;

  // History properties
  historyList: any[] = [];
  showAllHistory: boolean = false;
  showHistoryTab: boolean = false;

  // Tab management
  activeTab: string = 'overview';

  private readonly modalService = inject(NgbModal);
  constructor(
    private flightService: FlightService,
    private worldService: WorldService,

    private toaster: ToastrService,
    private route: ActivatedRoute,
    private location: Location
  ) {
  }
  async ngOnInit() {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');;
      if (id) {
        this.loadDetails(id);
      }
    });
  }


  getTimeFromDateTime(dateTime: string): string {
    const date = new Date(dateTime);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  }
  convertDurationToHour(duration: number): string {
    const hours = Math.floor(duration / 60).toString().padStart(2, '0');
    const minutes = (duration % 60).toString().padStart(2, '0');
    return `${hours}h${minutes}`;
  }

  formatDateTo_ddMMyyyy(date: string): string | null {
    return formatDateTo_ddMMyyyy(date);
  }
  pricePaxInfor: any = [];
  getPricePax() {
    const typePax = ['ADT', 'CHD', 'INF'];
    let data;

    if (this.orderDetails?.full?.InventoriesSelected[0].combine &&
      this.orderDetails?.full?.InventoriesSelected.length > 1) {
      data = this.orderDetails?.full?.InventoriesSelected[1].inventorySelected?.FareInfos;
    } else {
      const result = typePax.map(paxType => ({
        PaxType: paxType,
        Fare: 0,
        Tax: 0
      }));

      this.orderDetails?.full?.InventoriesSelected.forEach((inventory: any) => {
        inventory.inventorySelected.FareInfos.forEach((fareInfo: any) => {
          if (typePax.includes(fareInfo.PaxType)) {
            const paxResult = result.find(r => r.PaxType === fareInfo.PaxType);
            if (paxResult) {
              paxResult.Fare += fareInfo.Fare;
              paxResult.Tax += fareInfo.Tax;
            }
          }
        });
      });

      // Remove entries with 0 count
      if (this.orderDetails?.full?.adult === 0) {
        result.splice(result.findIndex(r => r.PaxType === 'ADT'), 1);
      }
      if (this.orderDetails?.full?.child === 0) {
        result.splice(result.findIndex(r => r.PaxType === 'CHD'), 1);
      }
      if (this.orderDetails?.full?.infant === 0) {
        result.splice(result.findIndex(r => r.PaxType === 'INF'), 1);
      }

      data = result;
    }
    this.pricePaxInfor = data;
  }
  getPassengerDescription(paxType: string): string {
    switch (paxType) {
      case 'ADT':
        return `${this.orderAvailable?.adult} x Người lớn`;
      case 'CHD':
        return `${this.orderAvailable?.child} x Trẻ em`;
      case 'INF':
        return `${this.orderAvailable?.infant} x Em bé`;
      default:
        return '';
    }
  }
  getDurationByArray(legs: any[]): string {
    if (!legs?.length) return '';

    const departure = new Date(legs[0].DepartureDate);
    const arrival = new Date(legs[legs.length - 1].ArrivalDate);
    const duration = arrival.getTime() - departure.getTime();

    const hours = Math.floor(duration / 3600000);
    const minutes = Math.floor((duration % 3600000) / 60000);

    return `${hours.toString().padStart(2, '0')}h${minutes.toString().padStart(2, '0')}`;
  }

  async loadDetails(id: string) {
    try {
      const res = await this.flightService.GetFlightRequestDetailsByID(id).toPromise();

      if (res.isSuccessed) {
        this.state = res.resultObj.code;
        this.orderDetails = JSON.parse(res.resultObj.note);
        console.log(this.orderDetails);
        this.orderAvailable = res.resultObj;

        // Load history data
        this.historyList = res.resultObj.history || [];

        if (this.orderAvailable.noteUser) {
          try {
            this.noteUserList = JSON.parse(this.orderAvailable.noteUser);
          } catch {
            this.noteUserList = [];
          }
        } else {
          this.noteUserList = [];
        }

        this.formatPassenger();
        await this.getInforAirports();
        this.getPricePax();
        this.servicePrice = this.getSumServicePrice();
        this.processPNRs();
      } else if (res.code === 409) {
        this.openModal('Thông báo', "Yêu cầu đang được xử lý bởi người dùng khác.");
      }else if(res.code === 403){
        this.openModal('Thông báo', "Bạn không có quyền xem thông tin này.");
      }else{
        this.openModal('Thông báo', res.message);
      }
    } catch (error) {
      // this.toaster.error('Failed to load flight details', 'Error');
    }
  }
  getSumServicePrice(): number {
    return this.orderDetails.paxList.reduce((sum: number, passenger: any) => {
      return sum + passenger.baggages.reduce((bagSum: number, baggage: any) => bagSum + baggage.Price, 0);
    }, 0);
  }
  formatPassenger() {
    let indexInfant = 0;
    const paxList = [...this.orderDetails.paxList];

    paxList.forEach((pax: any, index: number) => {
      if (pax.type === 'infant') {
        const paxAdult = paxList.find((p: any) => p.type === 'adult' && p.index === indexInfant);
        if (paxAdult) {
          paxAdult.withInfant = pax;
          this.orderDetails.paxList.splice(index, 1);
        }
        indexInfant++;
      } else {
        pax.index = index;
      }
    });
  }
  async getInforAirports() {
    const airportsCode = new Set<string>();

    this.orderDetails.full?.InventoriesSelected.forEach((inventory: any) => {
      inventory.segment.Legs.forEach((leg: any) => {
        airportsCode.add(leg.DepartureCode);
        airportsCode.add(leg.ArrivalCode);
      });
    });

    try {
      const res = await lastValueFrom(
        this.worldService.getAirportInfoByCode(Array.from(airportsCode), 'vi')
      );

      if (res.isSuccessed) {
        this.inforAirports = res.resultObj;
      }
    } catch (error) {
      console.error('Failed to fetch airport information:', error);
    }
  }

  async cancelRequest() {
    if (confirm("Are you sure to reject this request?")) {
      try {
        const res = await this.flightService.updateStatusFlightRequest(this.orderAvailable.id, -1).toPromise();

        if (res.isSuccessed) {
          this.showSuccessToast(res.message);
          this.location.back();
        } else {
          this.showErrorToast(res.message);
        }
      } catch (error) {
        this.showErrorToast('Failed to cancel request');
      }
    }
  }
  // Modal methods
  openModal(title: string = 'Thông báo', content: string = '') {
    this.titleModal = title;
    this.contentModal = content;
    this.modalService.open(this.modalNotification, { backdrop: 'static' });
  }


  openModalNote(isOpen: boolean) {
    if (isOpen) {
      this.modalService.open(this.modalNote, { backdrop: 'static', size: 'minw' });
    } else {
      this.modalService.dismissAll();
    }
  }

  goBack() {
    this.modalService.dismissAll();
    this.location.back();
  }

  async onSubmitNews() {
    if (confirm("Are you sure to submit this request?")) {
      try {
        const res = await this.flightService.updateStatusFlightRequest(this.orderAvailable.id, 1).toPromise();

        if (res.isSuccessed) {
          this.showSuccessToast(res.message);
          this.location.back();
        } else {
          this.showErrorToast(res.message);
        }
      } catch (error) {
        this.showErrorToast('Failed to submit request');
      }
    }
  }

  // Toast Helpers
  private showSuccessToast(message: string) {
    this.toaster.success(message, "Success", {
      timeOut: 3000,
      progressBar: true,
      progressAnimation: 'increasing',
      positionClass: 'toast-top-right'
    });
  }

  private showErrorToast(message: string) {
    this.toaster.error(message, "Failed", {
      timeOut: 3000,
      progressBar: true,
      progressAnimation: 'increasing',
      positionClass: 'toast-top-right'
    });
  }

  // Add method to process PNRs
  private processPNRs() {
    this.pnrList = [];

    // If no PNRs, show airline + fail status
    if (!this.orderAvailable.pnrs) {
      if (this.orderAvailable.airlines) {
        console.log('this.orderAvailable.airlines', this.orderAvailable.airlines);
        const airlines = this.orderAvailable.airlines.split(';').filter((airline: string) => airline.trim());
        airlines.forEach((airline: string) => {
          this.pnrList.push({
            pnr: '',
            system: airline,
            status: 'Failed'
          });
        });
      }
      return;
    }

    console.log('this.orderAvailable.airlines', this.orderAvailable.airlines);
    // Process PNRs if they exist
    const pnrs = this.orderAvailable.pnrs.split(',').filter((pnr: string) => pnr.trim());
    const airlines = this.orderAvailable.airlines ?
      this.orderAvailable.airlines.split(';').filter((airline: string) => airline.trim()) : [];

    if (pnrs.length > 0) {
      pnrs.forEach((pnr: string, index: number) => {
        this.pnrList.push({
          pnr: pnr,
          system: airlines[index] || 'Unknown',
          status: pnr.length === 6 ? 'Success' : 'Failed'
        });
      });
    }
  }

  // Update PNR formatting method
  formatPNR(pnr: string): string {
    if (!pnr) return '';
    return pnr.split('').join(' ');
  }

  async addNoteUser() {
    if (!this.newNoteContent?.trim()) return;

    const note = {
      content: this.newNoteContent,
      time: new Date().toISOString()
    };

    // Lấy danh sách cũ, thêm mới vào
    const noteUserArr = this.noteUserList ? [...this.noteUserList] : [];
    noteUserArr.push(note);

    // Kiểm tra tổng độ dài JSON.stringify trước khi gọi API
    const jsonString = JSON.stringify(noteUserArr);
    if (jsonString.length > 500) {
      this.showErrorToast('Tổng độ dài tất cả ghi chú không được vượt quá 500 ký tự!');
      return;
    }

    try {
      const body = {
        orderCode: this.orderAvailable.id,
        noteUser: JSON.stringify(noteUserArr)
      };
      // Gọi API (giả sử bạn có flightService.addNoteUser)
      const res = await this.flightService.addNoteUser(body).toPromise();
      if (res.isSuccessed) {
        this.noteUserList = noteUserArr;
        this.newNoteContent = '';
        this.showSuccessToast('Ghi chú đã được lưu!');
        this.openModalNote(false); // Ẩn modal ghi chú sau khi lưu thành công
      } else {
        this.showErrorToast('Lưu ghi chú thất bại!');
      }
    } catch {
      this.showErrorToast('Lỗi khi lưu ghi chú!');
    }
  }

  deleteNoteUser(index: number) {
    if (!confirm('Bạn có chắc chắn muốn xóa ghi chú này?')) return;
    const noteUserArr = [...this.noteUserList];
    noteUserArr.splice(index, 1);
    this.saveNoteUserArr(noteUserArr, 'Đã xóa ghi chú!');
  }

  startEditNoteUser(index: number) {
    this.editNoteIndex = index;
    this.editNoteContent = this.noteUserList[index].content;
  }

  saveEditNoteUser(index: number) {
    const noteUserArr = [...this.noteUserList];
    noteUserArr[index].content = this.editNoteContent;
    noteUserArr[index].time = new Date().toISOString();
    this.saveNoteUserArr(noteUserArr, 'Đã cập nhật ghi chú!');
    this.editNoteIndex = null;
    this.editNoteContent = '';
  }

  cancelEditNoteUser() {
    this.editNoteIndex = null;
    this.editNoteContent = '';
  }

  async saveNoteUserArr(noteUserArr: any[], successMsg: string) {
    // Tính tổng độ dài JSON.stringify(noteUserArr)
    const jsonString = JSON.stringify(noteUserArr);
    if (jsonString.length > 500) {
      this.showErrorToast('Tổng độ dài tất cả ghi chú không được vượt quá 500 ký tự!');
      return;
    }
    try {
      const body = {
        orderCode: this.orderAvailable.id,
        noteUser: jsonString
      };
      const res = await this.flightService.addNoteUser(body).toPromise();
      if (res.isSuccessed) {
        this.noteUserList = noteUserArr;
        this.showSuccessToast(successMsg);
      } else {
        this.showErrorToast('Thao tác thất bại!');
      }
    } catch {
      this.showErrorToast('Lỗi khi thao tác ghi chú!');
    }
  }

  getTotalNoteContentLength(editing: boolean = false, newContent: string = '', editIndex: number | null = null): number {
    let arr = [...this.noteUserList];
    if (editing && editIndex !== null) {
      // Đang sửa: thay content tại index = editIndex bằng newContent
      arr = arr.map((n, idx) => idx === editIndex ? { ...n, content: newContent } : n);
    } else if (editing && editIndex === null) {
      // Đang thêm mới: thêm note mới vào cuối
      if (newContent && newContent.trim()) {
        arr = [...arr, { content: newContent, time: new Date().toISOString() }];
      }
    }
    return JSON.stringify(arr).length;
  }

  // History helper methods
  getActionIcon(action: string): string {
    switch (action) {
      case 'VIEW_DETAILS_FULL':
        return '👁️';
      case 'UPDATE_STATUS':
        return '🔄';
      case 'CREATE':
        return '✅';
      case 'CANCEL':
        return '❌';
      case 'APPROVE':
        return '✅';
      case 'REJECT':
        return '❌';
      default:
        return '📝';
    }
  }

  getActionColor(action: string): string {
    switch (action) {
      case 'VIEW_DETAILS_FULL':
        return 'bg-blue-100 text-blue-600';
      case 'UPDATE_STATUS':
        return 'bg-orange-100 text-orange-600';
      case 'CREATE':
        return 'bg-green-100 text-green-600';
      case 'CANCEL':
      case 'REJECT':
        return 'bg-red-100 text-red-600';
      case 'APPROVE':
        return 'bg-green-100 text-green-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  }

  getActionText(action: string): string {
    switch (action) {
      case 'VIEW_DETAILS_FULL':
        return 'Xem chi tiết';
      case 'UPDATE_STATUS':
        return 'Cập nhật trạng thái';
      case 'CREATE':
        return 'Tạo yêu cầu';
      case 'CANCEL':
        return 'Hủy yêu cầu';
      case 'APPROVE':
        return 'Phê duyệt';
      case 'REJECT':
        return 'Từ chối';
      default:
        return action;
    }
  }
}