import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ScreenService } from '../../../service/screen/screen.service';


@Component({
  selector: 'app-address-banner',
  imports: [
    RouterLink,
    RouterLinkActive,
    FormsModule,
    CommonModule
  ],
  templateUrl: './address-banner.component.html',
  styleUrl: './address-banner.component.css'
})
export class AddressBannerComponent {
  submitted = false;

  translatePage: any;

  constructor(
    public translate: TranslateService,
    private readonly screenService: ScreenService,
    private router: Router,
  ) {
  }

  async ngOnInit() {
    const lang = localStorage.getItem('lang');
    this.translate.use(lang || 'vi');
    await this.loadTransLate(this.translate.currentLang);

    this.translate.onLangChange.subscribe(() => {
      this.loadTransLate(this.translate.currentLang);
    });
  }
  async loadTransLate(lang: string = 'vi') {
    var request = {
      Language: lang,
      ScreenCode: 'S0004-0031'
    }
    const observable = await this.screenService.getPageTranslate(request);
    const res = await observable.toPromise();
    try {
      if (res.isSuccessed) {
        this.translatePage = res.resultObj;
      }
    } catch (error) {
      console.log(error);
    }

  }

  onSubmit(f: NgForm) {
    this.submitted = true;
    const keyword = f.value.keyword;
    if (!keyword) {
      return;
    }
    this.router.navigateByUrl('/address?keyword=' + keyword);
  }
}
