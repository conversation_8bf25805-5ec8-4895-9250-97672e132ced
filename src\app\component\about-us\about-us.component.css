.bg-custom {
    mask-image: linear-gradient(180deg, rgba(0, 0, 0, 1) 72%, rgba(0, 0, 0, 0));
    mask-repeat: no-repeat;
    background-position: top left, bottom center;
    background-repeat: no-repeat, repeat;
    background-size: cover;
}

.hexagon {
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hexagon-text {
    @apply flex flex-col items-start justify-center;
}


:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 0, 0, 0;
        --background-end-rgb: 0, 0, 0;
    }
}

body {
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(to bottom,
            transparent,
            rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}