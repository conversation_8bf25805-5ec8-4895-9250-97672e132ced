<svg width="240" height="140" viewBox="0 0 240 140" fill="none" xmlns="http://www.w3.org/2000/svg">
  <style>
    /* Animation cho viền đứt nét chạy quanh */
    .dashed-anim {
      stroke-dasharray: 4,6;
      stroke-dashoffset: 0;
      animation: dashmove 1.5s linear infinite;
    }
    @keyframes dashmove {
      to {
        stroke-dashoffset: -10;
      }
    }
    /* Animation cho chữ eSIM */
    .esim-text {
       opacity: 1;
        transform: scale(1);
    }
   
  </style>
  <!-- Viền trắng ngoài cùng, nét đứt nhỏ, có animation -->
  <path d="M65 18 L214 18 Q220 18 220 30 V124 Q220 136 208 136 H28 Q18 136 18 124 V65 L65 18 Z"
        fill="none" stroke="#fff" stroke-width="2"  class="dashed-anim"/>
  <!-- Viền trắng liền đều -->
  <path d="M66 28 L204 28 Q212 28 212 36 V116 Q212 128 200 128 H36 Q24 128 24 116 V70 L66 28 Z"
        fill="none" stroke="#fff" stroke-width="4"/>
  <!-- Nền cam -->
  <path d="M46 28 L204 28 Q212 28 212 36 V116 Q212 128 200 128 H36 Q24 128 24 116 V70 L66 28 Z"
        fill="#ff980091"/>
  <!-- Chữ eSIM lớn, giữa SIM, có animation -->
  <text x="50%" y="58%" text-anchor="middle" fill="#fff" font-size="56" font-family="Arial, Helvetica, sans-serif" font-weight="bold" dy=".35em" class="esim-text">eSIM</text>
  <!-- Ký hiệu Ngọc Mai (NM) - nhỏ, vàng, góc dưới phải nền cam, có viền trắng -->
<g>
  <text x="192" y="120"
        text-anchor="middle"
        fill="#FFD600"
        stroke="#fff"
        stroke-width="1.2"
        font-size="20"
        font-family="Arial Rounded MT Bold, Arial, sans-serif"
        font-weight="bold"
        opacity="0.95"
        style="letter-spacing:2px; filter: drop-shadow(0 1px 2px #bfa00055);">
    NM
  </text>
</g>
</svg> 