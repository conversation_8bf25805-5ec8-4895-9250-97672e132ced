import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AddressService } from '../../../service/address/address.service';
import { EmailContactComponent } from '../../common/email-contact/email-contact.component';
import { NewsService } from '../../../service/news/news.service';
import { AddressBannerComponent } from '../address-banner/address-banner.component';

@Component({
  selector: 'app-address-home',
  imports: [
    CommonModule,
    RouterLink,
    EmailContactComponent,
    AddressBannerComponent
  ],
  templateUrl: './address-home.component.html',
  styleUrl: './address-home.component.css'
})

export class AddressHomeComponent {
  newsDefault: any = [];
  dataResult: any = {
    pageCount: 1,
    pageIndex: 1,
    pageSize: 20,
    id: '',
    totalRecords: 0,
    typeScreen: "",
    items: []
  };

  request: any = {
    PageIndex: 1,
    PageSize: 10,
    Keyword: '',
    type: 'inCountry'
  };
  startIndex: number = Math.max(this.dataResult.pageIndex - 5, 1);
  finishIndex: number = Math.min(this.dataResult.pageIndex + 5, this.dataResult.pageCount);

  constructor(
    private addressService: AddressService,
    private newsService: NewsService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['keyword'] !== undefined) {
        this.request.Keyword = params['keyword'];
        this.request.type = '';
      } else {
        this.request.Keyword = '';
      }
      if (params['type'] !== undefined) {
        this.request.type = params['type'];
      }
      this.productSearch();
    });
  }
  ngAfterViewInit() {
    this.loadNewsDefault(8);
  }

  productSearch() {
    this.addressService.search(this.request).subscribe((res) => {
      this.dataResult = res.resultObj;
      this.startIndex = Math.max(this.dataResult.pageIndex - 5, 1);
      this.finishIndex = Math.min(this.dataResult.pageIndex + 5, this.dataResult.pageCount);
    });
  }

  loadNewsDefault(quantity: number) {
    this.newsService.getNewsByType('du-lich', quantity).subscribe((res) => {
      this.newsDefault = res.resultObj;
    });
  }
  clickType(type: string) {
    this.request.type = type;
    this.request.Keyword = '';
    this.router.navigate(['/address'], { queryParams: { type: type } });
    // this.productSearch();
  }
  isTypeActive(type: string) {
    return this.request.type === type;
  }

  range(start: number, end: number): number[] {
    return Array(end - start + 1).fill(0).map((_, i) => start + i);
  }

  getUrl(page: number) {
    this.request.PageIndex = page;
    this.productSearch();
  }
  formatDate(date: Date): string | null {
    var date1 = new Date(date)
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric'
    }).format(date1);
  }
}
